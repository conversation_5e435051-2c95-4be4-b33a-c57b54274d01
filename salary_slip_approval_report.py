# Salary Slip Approval Report for Frappe Desk
# This script should be pasted into the Script section of a Script Report in Frappe Desk

import frappe

# Define columns (global scope)
columns = [
    {
        "fieldname": "name",
        "label": "Salary Slip",
        "fieldtype": "Link",
        "options": "Salary Slip",
        "width": 120
    },
    {
        "fieldname": "employee",
        "label": "Employee",
        "fieldtype": "Link",
        "options": "Employee",
        "width": 100
    },
    {
        "fieldname": "first_name",
        "label": "First Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "last_name",
        "label": "Last Name",
        "fieldtype": "Data",
        "width": 100
    },
    {
        "fieldname": "designation",
        "label": "Designation",
        "fieldtype": "Data",
        "width": 120
    },
    {
        "fieldname": "basic",
        "label": "Basic",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "overtime",
        "label": "Overtime",
        "fieldtype": "Currency",
        "width": 100
    },
    {
        "fieldname": "gross_pay",
        "label": "Gross Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "net_pay",
        "label": "Net Pay",
        "fieldtype": "Currency",
        "width": 120
    },
    {
        "fieldname": "status",
        "label": "Status",
        "fieldtype": "Data",
        "width": 100
    }
]

# Build query using Frappe Query Builder
SalarySlip = frappe.qb.DocType('Salary Slip')
Employee = frappe.qb.DocType('Employee')

# Main query to get salary slips with employee details
query = (
    frappe.qb.from_(SalarySlip)
    .left_join(Employee)
    .on(SalarySlip.employee == Employee.name)
    .select(
        SalarySlip.name,
        SalarySlip.employee,
        SalarySlip.designation,
        SalarySlip.gross_pay,
        SalarySlip.net_pay,
        SalarySlip.status,
        Employee.first_name,
        Employee.last_name,
        Employee.designation.as_('emp_designation')
    )
    .where(SalarySlip.docstatus == 0)
    .orderby(SalarySlip.creation, order=frappe.qb.desc)
    .limit(100)
)

# Execute the main query
salary_slips = query.run(as_dict=True)

# Get salary components for each slip using Query Builder
SalaryDetail = frappe.qb.DocType('Salary Detail')

for slip in salary_slips:
    # Get Basic salary component using Query Builder
    basic_query = (
        frappe.qb.from_(SalaryDetail)
        .select(SalaryDetail.amount)
        .where(
            (SalaryDetail.parent == slip.name) &
            (SalaryDetail.parentfield == 'earnings') &
            (SalaryDetail.salary_component.isin(['Basic', 'Basic Salary', 'Base']))
        )
        .limit(1)
    )

    basic_result = basic_query.run()
    slip.basic = frappe.utils.flt(basic_result[0][0]) if basic_result else 0

    # Get Overtime component using Query Builder
    overtime_query = (
        frappe.qb.from_(SalaryDetail)
        .select(SalaryDetail.amount)
        .where(
            (SalaryDetail.parent == slip.name) &
            (SalaryDetail.parentfield == 'earnings') &
            (SalaryDetail.salary_component.isin(['Overtime', 'Over Time', 'OT']))
        )
        .limit(1)
    )

    overtime_result = overtime_query.run()
    slip.overtime = frappe.utils.flt(overtime_result[0][0]) if overtime_result else 0

    # Use employee designation if salary slip designation is empty
    if not slip.designation:
        slip.designation = slip.emp_designation

# Assign data for Frappe Desk (global scope)
data = salary_slips

# Optional: Set other report variables (global scope)
message = None
chart = None
report_summary = None
skip_total_row = False


