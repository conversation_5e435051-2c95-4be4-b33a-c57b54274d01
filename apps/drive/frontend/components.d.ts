/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AccessButton: typeof import('./src/components/ShareDialog/AccessButton.vue')['default']
    ActivityTree: typeof import('./src/components/ActivityTree.vue')['default']
    ActivityTreeItem: typeof import('./src/components/ActivityTreeItem.vue')['default']
    ActivityTreeShare: typeof import('./src/components/ActivityTreeShare.vue')['default']
    AlignCenter: typeof import('./src/components/DocEditor/icons/AlignCenter.vue')['default']
    AlignItemCenter: typeof import('./src/components/DocEditor/icons/align-item-center.vue')['default']
    AlignItemLeft: typeof import('./src/components/DocEditor/icons/align-item-left.vue')['default']
    AlignItemRight: typeof import('./src/components/DocEditor/icons/align-item-right.vue')['default']
    AlignJustify: typeof import('./src/components/DocEditor/icons/AlignJustify.vue')['default']
    AlignLeft: typeof import('./src/components/DocEditor/icons/AlignLeft.vue')['default']
    AlignRight: typeof import('./src/components/DocEditor/icons/AlignRight.vue')['default']
    AnnotationList: typeof import('./src/components/DocEditor/components/AnnotationList.vue')['default']
    AppsIcon: typeof import('./src/components/AppsIcon.vue')['default']
    AppSwitcher: typeof import('./src/components/AppSwitcher.vue')['default']
    Archive: typeof import('./src/components/MimeIcons/Archive.vue')['default']
    Audio: typeof import('./src/components/MimeIcons/Audio.vue')['default']
    AudioPreview: typeof import('./src/components/FileTypePreview/AudioPreview.vue')['default']
    BlockQuote: typeof import('./src/components/DocEditor/icons/BlockQuote.vue')['default']
    Bold: typeof import('./src/components/DocEditor/icons/Bold.vue')['default']
    BottomBar: typeof import('./src/components/BottomBar.vue')['default']
    Check: typeof import('./src/components/DocEditor/icons/Check.vue')['default']
    Codeblock: typeof import('./src/components/DocEditor/icons/Codeblock.vue')['default']
    ColorInput: typeof import('./src/components/DocEditor/components/ColorInput.vue')['default']
    ColorPicker: typeof import('./src/components/DocEditor/components/ColorPicker.vue')['default']
    ContextMenu: typeof import('./src/components/ContextMenu.vue')['default']
    CTADeleteDialog: typeof import('./src/components/CTADeleteDialog.vue')['default']
    CustomListRow: typeof import('./src/components/CustomListRow.vue')['default']
    CustomListRowItem: typeof import('./src/components/CustomListRowItem.vue')['default']
    DeleteDialog: typeof import('./src/components/DeleteDialog.vue')['default']
    Details: typeof import('./src/components/DocEditor/icons/Details.vue')['default']
    Dialogs: typeof import('./src/components/Dialogs.vue')['default']
    DocMenuAndInfoBar: typeof import('./src/components/DocEditor/components/DocMenuAndInfoBar.vue')['default']
    Document: typeof import('./src/components/MimeIcons/Document.vue')['default']
    DriveToolBar: typeof import('./src/components/DriveToolBar.vue')['default']
    EditTagDialog: typeof import('./src/components/Settings/EditTagDialog.vue')['default']
    ErrorPage: typeof import('./src/components/ErrorPage.vue')['default']
    FilePicker: typeof import('./src/components/FilePicker.vue')['default']
    FileRender: typeof import('./src/components/FileRender.vue')['default']
    FileUploader: typeof import('./src/components/FileUploader.vue')['default']
    FloatItemLeft: typeof import('./src/components/DocEditor/icons/float-item-left.vue')['default']
    FloatItemRight: typeof import('./src/components/DocEditor/icons/float-item-right.vue')['default']
    Folder: typeof import('./src/components/MimeIcons/Folder.vue')['default']
    FrappeDriveLogo: typeof import('./src/components/FrappeDriveLogo.vue')['default']
    GeneralAccess: typeof import('./src/components/GeneralAccess.vue')['default']
    GeneralDialog: typeof import('./src/components/GeneralDialog.vue')['default']
    GenericPage: typeof import('./src/components/GenericPage.vue')['default']
    GridItem: typeof import('./src/components/GridItem.vue')['default']
    GridView: typeof import('./src/components/GridView.vue')['default']
    Image: typeof import('./src/components/DocEditor/icons/Image.vue')['default']
    ImagePreview: typeof import('./src/components/FileTypePreview/ImagePreview.vue')['default']
    Indent: typeof import('./src/components/DocEditor/icons/Indent.vue')['default']
    InfoPopup: typeof import('./src/components/InfoPopup.vue')['default']
    InfoSidebar: typeof import('./src/components/InfoSidebar.vue')['default']
    InsertImage: typeof import('./src/components/DocEditor/components/InsertImage.vue')['default']
    InsertLink: typeof import('./src/components/DocEditor/components/InsertLink.vue')['default']
    InsertVideo: typeof import('./src/components/DocEditor/components/InsertVideo.vue')['default']
    Italic: typeof import('./src/components/DocEditor/icons/Italic.vue')['default']
    LineHeight: typeof import('./src/components/DocEditor/icons/line-height.vue')['default']
    List: typeof import('./src/components/DocEditor/icons/List.vue')['default']
    ListView: typeof import('./src/components/ListView.vue')['default']
    LucideAlertCircle: typeof import('~icons/lucide/alert-circle')['default']
    LucideArrowBigLeft: typeof import('~icons/lucide/arrow-big-left')['default']
    LucideArrowDownAz: typeof import('~icons/lucide/arrow-down-az')['default']
    LucideArrowRight: typeof import('~icons/lucide/arrow-right')['default']
    LucideArrowUpZa: typeof import('~icons/lucide/arrow-up-za')['default']
    LucideAxe: typeof import('~icons/lucide/axe')['default']
    LucideBuilding: typeof import('~icons/lucide/building')['default']
    LucideCheck: typeof import('~icons/lucide/check')['default']
    LucideChevronDown: typeof import('~icons/lucide/chevron-down')['default']
    LucideChevronRight: typeof import('~icons/lucide/chevron-right')['default']
    LucideClock: typeof import('~icons/lucide/clock')['default']
    LucideEllipsis: typeof import('~icons/lucide/ellipsis')['default']
    LucideEyeOff: typeof import('~icons/lucide/eye-off')['default']
    LucideFile: typeof import('~icons/lucide/file')['default']
    LucideFilter: typeof import('~icons/lucide/filter')['default']
    LucideFolderClosed: typeof import('~icons/lucide/folder-closed')['default']
    LucideFolderOpenDot: typeof import('~icons/lucide/folder-open-dot')['default']
    LucideFolderPlus: typeof import('~icons/lucide/folder-plus')['default']
    LucideGlobe2: typeof import('~icons/lucide/globe2')['default']
    LucideHome: typeof import('~icons/lucide/home')['default']
    LucideImage: typeof import('~icons/lucide/image')['default']
    LucideInfo: typeof import('~icons/lucide/info')['default']
    LucideItalic: typeof import('~icons/lucide/italic')['default']
    LucideLink2: typeof import('~icons/lucide/link2')['default']
    LucideLock: typeof import('~icons/lucide/lock')['default']
    LucideLogOut: typeof import('~icons/lucide/log-out')['default']
    LucideMinus: typeof import('~icons/lucide/minus')['default']
    LucideMoreHorizontal: typeof import('~icons/lucide/more-horizontal')['default']
    LucidePlus: typeof import('~icons/lucide/plus')['default']
    LucideSearch: typeof import('~icons/lucide/search')['default']
    LucideSettings: typeof import('~icons/lucide/settings')['default']
    LucideStar: typeof import('~icons/lucide/star')['default']
    LucideTag: typeof import('~icons/lucide/tag')['default']
    LucideTrash: typeof import('~icons/lucide/trash')['default']
    LucideUser: typeof import('~icons/lucide/user')['default']
    LucideX: typeof import('~icons/lucide/x')['default']
    Mention: typeof import('./src/components/DocEditor/icons/Mention.vue')['default']
    MentionList: typeof import('./src/components/DocEditor/components/MentionList.vue')['default']
    Menu: typeof import('./src/components/DocEditor/components/Menu.vue')['default']
    MoveDialog: typeof import('./src/components/MoveDialog.vue')['default']
    MSOfficePreview: typeof import('./src/components/FileTypePreview/MSOfficePreview.vue')['default']
    Navbar: typeof import('./src/components/Navbar.vue')['default']
    NewAnnotation: typeof import('./src/components/DocEditor/components/NewAnnotation.vue')['default']
    NewComment: typeof import('./src/components/DocEditor/components/NewComment.vue')['default']
    NewFolderDialog: typeof import('./src/components/NewFolderDialog.vue')['default']
    NewLinkDialog: typeof import('./src/components/NewLinkDialog.vue')['default']
    NewManualSnapshotDialog: typeof import('./src/components/DocEditor/components/NewManualSnapshotDialog.vue')['default']
    NewTagDialog: typeof import('./src/components/Settings/NewTagDialog.vue')['default']
    NoFilesSection: typeof import('./src/components/NoFilesSection.vue')['default']
    OrderList: typeof import('./src/components/DocEditor/icons/OrderList.vue')['default']
    Outdent: typeof import('./src/components/DocEditor/icons/Outdent.vue')['default']
    OuterComment: typeof import('./src/components/DocEditor/components/OuterComment.vue')['default']
    PageBreak: typeof import('./src/components/DocEditor/icons/PageBreak.vue')['default']
    PDF: typeof import('./src/components/MimeIcons/PDF.vue')['default']
    PDFPreview: typeof import('./src/components/FileTypePreview/PDFPreview.vue')['default']
    Presentation: typeof import('./src/components/MimeIcons/Presentation.vue')['default']
    PreviewEditor: typeof import('./src/components/DocEditor/PreviewEditor.vue')['default']
    PrimaryDropdown: typeof import('./src/components/PrimaryDropdown.vue')['default']
    ProfileSettings: typeof import('./src/components/Settings/ProfileSettings.vue')['default']
    ProgressRing: typeof import('./src/components/ProgressRing.vue')['default']
    RenameDialog: typeof import('./src/components/RenameDialog.vue')['default']
    ResizableMediaNodeView: typeof import('./src/components/DocEditor/components/ResizableMediaNodeView.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SearchPopup: typeof import('./src/components/SearchPopup.vue')['default']
    SettingsDialog: typeof import('./src/components/Settings/SettingsDialog.vue')['default']
    ShareDialog: typeof import('./src/components/ShareDialog/ShareDialog.vue')['default']
    ShortcutsDialog: typeof import('./src/components/ShortcutsDialog.vue')['default']
    Sidebar: typeof import('./src/components/Sidebar.vue')['default']
    SidebarItem: typeof import('./src/components/SidebarItem.vue')['default']
    SnapshotPreviewDialog: typeof import('./src/components/DocEditor/components/SnapshotPreviewDialog.vue')['default']
    Spreadsheet: typeof import('./src/components/MimeIcons/Spreadsheet.vue')['default']
    StorageBar: typeof import('./src/components/StorageBar.vue')['default']
    StorageSettings: typeof import('./src/components/Settings/StorageSettings.vue')['default']
    StrikeThrough: typeof import('./src/components/DocEditor/icons/StrikeThrough.vue')['default']
    Style: typeof import('./src/components/DocEditor/icons/Style.vue')['default']
    SuggestionList: typeof import('./src/components/DocEditor/components/suggestionList.vue')['default']
    TableBubbleMenu: typeof import('./src/components/DocEditor/components/TableBubbleMenu.vue')['default']
    TableCellMenu: typeof import('./src/components/DocEditor/components/TableCellMenu.vue')['default']
    TableColumnMenu: typeof import('./src/components/DocEditor/components/TableColumnMenu.vue')['default']
    TableRowMenu: typeof import('./src/components/DocEditor/components/TableRowMenu.vue')['default']
    Tag: typeof import('./src/components/Tag.vue')['default']
    TagColorInput: typeof import('./src/components/TagColorInput.vue')['default']
    TagInput: typeof import('./src/components/TagInput.vue')['default']
    TagSettings: typeof import('./src/components/Settings/TagSettings.vue')['default']
    TeamSwitcher: typeof import('./src/components/TeamSwitcher.vue')['default']
    TextEditor: typeof import('./src/components/DocEditor/TextEditor.vue')['default']
    TextPreview: typeof import('./src/components/FileTypePreview/TextPreview.vue')['default']
    TiptapInput: typeof import('./src/components/TiptapInput.vue')['default']
    Toast: typeof import('./src/components/Toast.vue')['default']
    ToggleHeaderCell: typeof import('./src/components/DocEditor/icons/ToggleHeaderCell.vue')['default']
    Underline: typeof import('./src/components/DocEditor/icons/Underline.vue')['default']
    Unknown: typeof import('./src/components/MimeIcons/Unknown.vue')['default']
    UploadTracker: typeof import('./src/components/UploadTracker.vue')['default']
    UserAutoComplete: typeof import('./src/components/ShareDialog/UserAutoComplete.vue')['default']
    UserListSettings: typeof import('./src/components/Settings/UserListSettings.vue')['default']
    UsersBar: typeof import('./src/components/UsersBar.vue')['default']
    Video: typeof import('./src/components/DocEditor/icons/Video.vue')['default']
    VideoPreview: typeof import('./src/components/FileTypePreview/VideoPreview.vue')['default']
  }
}
