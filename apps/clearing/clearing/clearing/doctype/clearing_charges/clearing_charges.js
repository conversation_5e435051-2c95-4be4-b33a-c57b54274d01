// Copyright (c) 2024, <PERSON> and contributors
// For license information, please see license.txt

frappe.ui.form.on("Clearing Charges", {
  clearing_file: function (frm) {
    // Clear the child table before fetching new data
    frm.clear_table("charges");

    // Fetch charges from related doctypes where 'paid_by_clearing_agent' is checked
    frappe.call({
      method: "frappe.client.get_list",
      args: {
        doctype: "TRA Clearance",
        filters: {
          clearing_file: frm.doc.clearing_file,
          paid_by_clearing_agent: 1,
        },
        fields: ["total_charges"],
      },
      callback: function (r) {
        if (r.message) {
          $.each(r.message, function (i, d) {
            let row = frm.add_child("charges");
            row.charge_type = "TRA Clearance";
            row.amount = d.total_charges;
            frm.refresh_field("charges");
          });
        }
      },
    });

    frappe.call({
      method: "frappe.client.get_list",
      args: {
        doctype: "Port Clearance",
        filters: {
          clearing_file: frm.doc.clearing_file,
          paid_by_clearing_agent: 1,
        },
        fields: ["total_charges"],
      },
      callback: function (r) {
        if (r.message) {
          $.each(r.message, function (i, d) {
            let row = frm.add_child("charges");
            row.charge_type = "Port Clearance";
            row.amount = d.total_charges;
            frm.refresh_field("charges");
          });
        }
      },
    });

    frappe.call({
      method: "frappe.client.get_list",
      args: {
        doctype: "Shipping Line Clearance",
        filters: {
          clearing_file: frm.doc.clearing_file,
          paid_by_clearing_agent: 1,
        },
        fields: ["total_charges"],
      },
      callback: function (r) {
        if (r.message) {
          $.each(r.message, function (i, d) {
            let row = frm.add_child("charges");
            row.charge_type = "Shipping Line Clearance";
            row.amount = d.total_charges;
            frm.refresh_field("charges");
          });
        }
      },
    });

    frappe.call({
      method: "frappe.client.get_list",
      args: {
        doctype: "Physical Verification",
        filters: {
          clearing_file: frm.doc.clearing_file,
          paid_by_clearing_agent: 1,
        },
        fields: ["total_charges"],
      },
      callback: function (r) {
        if (r.message) {
          $.each(r.message, function (i, d) {
            let row = frm.add_child("charges");
            row.charge_type = "Physical Verification";
            row.amount = d.total_charges;
            frm.refresh_field("charges");
          });
        }
      },
    });
  },

  generate_debit_note: function (frm) {
    // Check if there are items in the child table
    if (frm.doc.charges && frm.doc.charges.length > 0) {
      // Call the server method to create debit note for charges
      frappe.call({
        method:
          "clearing.clearing.doctype.clearing_charges.clearing_charges.create_debit_note",
        args: {
          clearing_charges_name: frm.doc.name,
        },
        callback: function (r) {
          if (r.message) {
            // Link the created Debit Note to the Clearing Charges
            frm.set_value("debit_note_number", r.message.name);
            frm.save();
            frappe.msgprint(
              __("Debit Note created successfully: {0}", [r.message.name])
            );
          }
        },
      });
    } else {
      frappe.msgprint(__("Please add charges to create a debit note."));
    }
  },

  generate_service_invoice: function (frm) {
    // Call the server method to create service invoice
    frappe.call({
      method:
        "clearing.clearing.doctype.clearing_charges.clearing_charges.create_service_invoice_from_service_charges",
      args: {
        clearing_charges_name: frm.doc.name,
      },
      callback: function (r) {
        if (r.message) {
          // Link the created Service Invoice to the Clearing Charges
          frm.set_value("invoice_number", r.message.name);
          frm.save();
          frappe.msgprint(
            __("Service Invoice {0} created successfully.", [r.message.name])
          );
        }
      },
    });
  },

  generate_journal_entry: function (frm) {
    // Check if there are items in the child table
    if (frm.doc.charges && frm.doc.charges.length > 0) {
      // Call the server method to create journal entry for charges
      frappe.call({
        method:
          "clearing.clearing.doctype.clearing_charges.clearing_charges.create_journal_entry_for_clearing_charges",
        args: {
          clearing_charges_name: frm.doc.name,
        },
        callback: function (r) {
          if (r.message) {
            // Link the created Journal Entry to the Clearing Charges
            frm.set_value("journal_entry", r.message.name);
            frm.save();
            frappe.msgprint(
              __("Journal Entry created successfully: {0}", [r.message.name])
            );
          }
        },
      });
    } else {
      frappe.msgprint(__("Please add charges to create a journal entry."));
    }
  },
});
