{"actions": [], "allow_rename": 1, "autoname": "field:location_name", "creation": "2025-04-03 17:58:32.560878", "doctype": "DocType", "engine": "InnoDB", "field_order": ["location_name", "address"], "fields": [{"fieldname": "location_name", "fieldtype": "Data", "label": "Location Name", "unique": 1}, {"fieldname": "address", "fieldtype": "Data", "label": "Address"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-04-09 16:11:29.803477", "modified_by": "Administrator", "module": "Clearing", "name": "Container Location", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}