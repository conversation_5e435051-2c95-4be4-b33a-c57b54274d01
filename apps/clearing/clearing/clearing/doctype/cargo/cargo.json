{"actions": [], "allow_rename": 1, "creation": "2024-07-15 22:58:39.021275", "default_view": "List", "doctype": "DocType", "engine": "InnoDB", "field_order": ["package_type", "hs_code", "container_number", "quantity_of_hs_code", "quantity_of_container", "column_break_1", "cargo_description", "number_of_packages", "seal_number", "column_break_2", "weight", "volume", "column_break_3", "value", "currency", "trading_country", "country_last_consignment", "country_of_destination"], "fields": [{"fieldname": "cargo_description", "fieldtype": "Data", "in_list_view": 1, "label": "Cargo Description", "reqd": 1}, {"fieldname": "hs_code", "fieldtype": "Data", "in_list_view": 1, "label": "HS Code"}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "weight", "fieldtype": "Float", "label": "Weight (Kg)"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "volume", "fieldtype": "Float", "label": "Volume (CBM)"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "value", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Value", "reqd": 1}, {"fieldname": "package_type", "fieldtype": "Select", "in_list_view": 1, "label": "Package Type", "options": "Container 20FT\nContainer 40FT\nLoose", "reqd": 1}, {"fieldname": "container_number", "fieldtype": "Data", "label": "Container Number"}, {"fieldname": "seal_number", "fieldtype": "Data", "label": "Seal Number"}, {"fieldname": "number_of_packages", "fieldtype": "Int", "label": "Number of packages"}, {"fieldname": "currency", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "trading_country", "fieldtype": "Link", "label": "Trading Country", "options": "Country"}, {"fieldname": "country_last_consignment", "fieldtype": "Link", "label": "Country Last Consignment", "options": "Country"}, {"fieldname": "quantity_of_container", "fieldtype": "Int", "label": "Quantity of Container"}, {"fieldname": "country_of_destination", "fieldtype": "Link", "label": "Country of Destination", "options": "Country"}, {"fieldname": "quantity_of_hs_code", "fieldtype": "Int", "label": "Quantity of HS Code"}], "index_web_pages_for_search": 1, "istable": 1, "links": [], "modified": "2025-04-15 12:09:56.621843", "modified_by": "Administrator", "module": "Clearing", "name": "Cargo", "owner": "Administrator", "permissions": [], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}