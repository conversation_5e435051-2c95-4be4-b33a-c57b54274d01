import frappe
from frappe.model.document import Document
from frappe import _
from clearing.clearing.doctype.port_clearance.port_clearance import ensure_all_documents_attached


class TRAClearance(Document):
    def before_save(self):
        """Before saving the document, check if invoice is paid and update the status."""
        if self.invoice_paid:
            # If the invoice is paid, automatically set the status to 'Payment Completed'
            self.status = "Payment Completed"
        else:
            # Reset the status if invoice is not paid (you can customize this logic)
            self.status = "Payment Pending"

        # Create journal entry if paid by clearing agent
        if self.paid_by_clearing_agent and self.total_charges and not self.get("journal_entry"):
            self.create_clearing_agent_journal_entry()

    def create_clearing_agent_journal_entry(self):
        """Create a journal entry when charges are paid by clearing agent"""
        if not self.total_charges or self.total_charges <= 0:
            return

        # Get default company
        company = frappe.defaults.get_user_default("Company") or frappe.defaults.get_global_default("company")
        if not company:
            frappe.throw(_("Please set a default company"))

        # Get default accounts
        clearing_agent_account = frappe.db.get_value("Company", company, "default_receivable_account")
        if not clearing_agent_account:
            frappe.throw(_("Please set default receivable account for company {0}").format(company))

        # Get or create TRA expense account
        expense_account = self.get_or_create_expense_account("TRA Clearance", company)

        # Create the journal entry
        journal_entry = frappe.new_doc("Journal Entry")
        journal_entry.voucher_type = "Journal Entry"
        journal_entry.posting_date = self.posting_date or frappe.utils.nowdate()
        journal_entry.company = company
        journal_entry.remark = f"TRA Clearance charges paid by agent for {self.clearing_file}"

        # Add debit entry (Clearing Agent Receivable)
        journal_entry.append("accounts", {
            "account": clearing_agent_account,
            "debit_in_account_currency": self.total_charges,
            "reference_type": "TRA Clearance",
            "reference_name": self.name
        })

        # Add credit entry (TRA Expense)
        journal_entry.append("accounts", {
            "account": expense_account,
            "credit_in_account_currency": self.total_charges,
            "reference_type": "TRA Clearance",
            "reference_name": self.name
        })

        # Save and submit the journal entry
        journal_entry.set_missing_values()
        journal_entry.save()
        journal_entry.submit()

        # Store reference to journal entry
        self.db_set('journal_entry', journal_entry.name, update_modified=False)

        frappe.msgprint(_("Journal Entry {0} created for TRA Clearance charges paid by agent").format(journal_entry.name))

    def get_or_create_expense_account(self, charge_type, company):
        """Get or create expense account for a specific charge type"""
        account_name = f"{charge_type} Expense - {frappe.db.get_value('Company', company, 'abbr')}"

        # Check if account already exists
        if frappe.db.exists("Account", account_name):
            return account_name

        # Get parent expense account
        parent_account = frappe.db.get_value("Company", company, "default_expense_account")
        if not parent_account:
            # Fallback to finding any expense account
            parent_account = frappe.db.get_value("Account", {
                "company": company,
                "account_type": "Expense Account",
                "is_group": 1
            }, "name")

        if not parent_account:
            frappe.throw(_("No parent expense account found for company {0}").format(company))

        # Create new expense account
        account = frappe.new_doc("Account")
        account.account_name = f"{charge_type} Expense"
        account.parent_account = parent_account
        account.company = company
        account.account_type = "Expense Account"
        account.is_group = 0
        account.save()

        return account.name

        
    def before_submit(self):
        # Ensure all required documents are attached before submission
        ensure_all_documents_attached(self, "tra_clearance_document")
        
        # Validate that the invoice is paid and status is set correctly
        self.validate_payment_status()
    
    def validate_payment_status(self):
        """Ensure payment status is marked as 'Payment Completed' before submission."""
        if self.status != "Payment Completed":
            frappe.throw(_("You cannot Complete TRA Clearance unless the Payment Completed."))
