{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-03-14 18:36:43.947773", "css": ".borderless {\n    font-size: inherit;\n    width: 100%;\n    max-width: 100%;\n    margin-bottom: 2rem; \n}\n\n.text-center {\n    text-align: center;\n}\n", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "CF Delivery Note", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<!--<div class=\\\"print-heading\\\">\\t\\t\\t\\t<h2><div>CF Delivery Note</div><br><small class=\\\"sub-heading\\\">{{ _(doc.name) }}</small>\\t\\t\\t\\t</h2></div>-->\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<!DOCTYPE html>\\n<html>\\n    <body>\\n        <div class='container p-4'>\\n            <div class='mb-5'>\\n                <h2 class='text-center'>DELIVERY NOTE</h2>\\n                <table class=\\\"borderless\\\">\\n                    <tr>\\n                        <td>DN #</td>\\n                        <td></td>\\n                        <td>Date {{ doc.posting_date }}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Consignee name: {{ doc.consignee }}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Address: {{ doc.address }}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>File No: {{ doc.clearing_file }}</td>\\n                        <td></td>\\n                        <td>Client: {{ doc.consignee }}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Loading Date: {{ doc.loading_date }}</td>\\n                        <td></td>\\n                        {% for item in doc.truck %}\\n                        <td>Truck/Trailer No: {{ item.truck_number }}</td>\\n                        {% endfor %}\\n                    </tr>\\n                    \\n                </table>\\n            </div>\\n            {% set clearing = frappe.get_doc(\\\"Clearing File\\\", doc.clearing_file) %}\\n            <div class='mb-5'>\\n                <h2 class='text-center'>PLEASE RECEIVE THE BELOW MENTIONED GOODS</h2>\\n                {% for detail in clearing.cargo_details%}\\n                <table class=\\\"borderless\\\">\\n                    <tr>\\n                        <td>Cargo Type:{{detail.package_type}}</td>\\n                        <td>Container No: {{detail.container_number}}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Gross Weight: {{detail.weight}}</td>\\n                        <td>Seal No: {{detail.seal_number}}</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Special Instructions: {{clearing.special_instructions}}</td>\\n                    </tr>\\n                </table>\\n                {% endfor %}\\n            </div>\\n            <div class='mb-5'>\\n                <h2 class='text-center'>ANY DAMAGE TO CARGO OR DELAY SHOULD BE REPORTED IMMEDIATELY</h2>\\n                <table class=\\\"borderless\\\">\\n                    <tr>\\n                        <td>Reported to {{doc.staff_name}} Agent</td>\\n                    </tr>\\n                    {% for item in doc.truck %}\\n                    <tr>\\n                        <td>Name: {{item.driver_name}}</td>\\n                        <td>Sign: ____________________________</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Date & Time: _________________________</td>\\n                        <td>Phone No: {{item.driver_contact_number}}</td>\\n                    </tr>\\n                    {%endfor%}\\n                </table>\\n            </div>\\n            <div class='mb-5'>\\n                <h2 class='text-center'>GOODS RECEIVED IN GOOD CONDITION</h2>\\n                <table class=\\\"borderless\\\">\\n                    <tr>\\n                        <td>Name: _______________________</td>\\n                        <td>Sign: _____________________________</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Date & Time: __________________________</td>\\n                        <td>Phone No: __________________________</td>\\n                    </tr>\\n                </table>\\n            </div>\\n            <div class='mb-5'>\\n                <h2>Delivery by</h2>\\n                <table class=\\\"borderless\\\">\\n                    <tr>\\n                        <td>Name: _______________________</td>\\n                        <td>Sign: ____________________________</td>\\n                    </tr>\\n                    <tr>\\n                        <td>Date & Time: __________________________</td>\\n                        <td>Phone No: ________________________</td>\\n                    </tr>\\n                </table>\\n            </div>\\n        </div>\\n    </body>\\n</html>\"}]", "idx": 0, "line_breaks": 0, "margin_bottom": 15.0, "margin_left": 15.0, "margin_right": 15.0, "margin_top": 15.0, "modified": "2025-04-10 15:50:32.712293", "modified_by": "Administrator", "module": "Clearing", "name": "Delivery Note", "owner": "Administrator", "page_number": "<PERSON>de", "pdf_generator": "wkhtmltopdf", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}