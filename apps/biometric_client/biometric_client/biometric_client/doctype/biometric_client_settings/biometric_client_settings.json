{"actions": [], "creation": "2025-01-10 17:44:00.161079", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["enable_biometric_master", "section_break_2", "server_url", "user_name", "password", "get_token", "bio_token", "column_break_6", "department", "area_code", "default_shift_type", "section_break_10", "auto_transactions", "column_break_12", "auto_checkin", "column_break_14", "auto_shift", "transactions_section", "start_time", "end_time", "get_transactions", "last_sync_time", "section_break_13", "make_employee_checkin"], "fields": [{"default": "0", "fieldname": "enable_biometric_master", "fieldtype": "Check", "label": "Enable Biometric Attendance"}, {"fieldname": "section_break_2", "fieldtype": "Section Break"}, {"fieldname": "server_url", "fieldtype": "Data", "label": " Server URL"}, {"fieldname": "user_name", "fieldtype": "Data", "label": "User Name"}, {"fieldname": "password", "fieldtype": "Data", "label": "Password"}, {"fieldname": "get_token", "fieldtype": "<PERSON><PERSON>", "label": "Get Token"}, {"fieldname": "bio_token", "fieldtype": "Data", "label": "Bio Token", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"default": "1", "fieldname": "department", "fieldtype": "Int", "label": "Default Department Code"}, {"default": "1", "fieldname": "area_code", "fieldtype": "Int", "label": "Default Area Code"}, {"fieldname": "default_shift_type", "fieldtype": "Link", "label": "Default Shift Type", "options": "Shift Type"}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "auto_transactions", "fieldtype": "Check", "label": "Enable Auto Transactions"}, {"fieldname": "column_break_12", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "auto_checkin", "fieldtype": "Check", "label": "Enable Auto Employee Checkin"}, {"fieldname": "column_break_14", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "auto_shift", "fieldtype": "Check", "label": "Enable Auto Shift Assignment"}, {"fieldname": "transactions_section", "fieldtype": "Section Break", "label": "Transactions"}, {"fieldname": "start_time", "fieldtype": "Datetime", "label": "Start Time"}, {"fieldname": "end_time", "fieldtype": "Datetime", "label": "End Time"}, {"fieldname": "get_transactions", "fieldtype": "<PERSON><PERSON>", "label": "Get Transactions"}, {"fieldname": "last_sync_time", "fieldtype": "Datetime", "label": "Last Sync Time"}, {"fieldname": "section_break_13", "fieldtype": "Section Break"}, {"fieldname": "make_employee_checkin", "fieldtype": "<PERSON><PERSON>", "label": "Make Employee Checkin"}], "issingle": 1, "links": [], "modified": "2025-01-10 17:44:00.161079", "modified_by": "Administrator", "module": "Biometric Client", "name": "Biometric Client Settings", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "System Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}