{"actions": [], "allow_rename": 1, "autoname": "format: ASG-SUB-{#####}", "creation": "2021-12-21 16:15:22.651658", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["assignment", "assignment_title", "type", "column_break_3", "member", "member_name", "evaluator", "section_break_dlzh", "assignment_attachment", "answer", "section_break_ydgh", "column_break_oqqy", "status", "comments", "section_break_rqal", "question", "column_break_esgd", "course", "lesson"], "fields": [{"fieldname": "lesson", "fieldtype": "Link", "label": "Lesson", "options": "Course Lesson"}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"fieldname": "assignment", "fieldtype": "Link", "label": "Assignment", "options": "LMS Assignment"}, {"fieldname": "member", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Member", "options": "User", "reqd": 1}, {"fetch_from": "member.full_name", "fieldname": "member_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Member Name", "read_only": 1}, {"fetch_from": "lesson.course", "fieldname": "course", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Course", "options": "LMS Course", "read_only": 1}, {"default": "Not Graded", "fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Pass\nFail\nNot Graded\nNot Applicable"}, {"fieldname": "comments", "fieldtype": "Text Editor", "label": "Comments"}, {"fieldname": "evaluator", "fieldtype": "Link", "label": "Evaluator", "options": "User"}, {"depends_on": "eval:!([\"URL\", \"Text\"]).includes(doc.type);", "fieldname": "assignment_attachment", "fieldtype": "Attach", "label": "Assignment Attachment", "mandatory_depends_on": "eval:doc.type != \"URL\";"}, {"fetch_from": "assignment.type", "fieldname": "type", "fieldtype": "Select", "in_list_view": 1, "label": "Type", "options": "Document\nPDF\nURL\nImage\nText"}, {"fetch_from": "assignment.question", "fieldname": "question", "fieldtype": "Text Editor", "label": "Question", "read_only": 1}, {"fetch_from": "assignment.title", "fieldname": "assignment_title", "fieldtype": "Data", "label": "Assignment Title"}, {"fieldname": "section_break_rqal", "fieldtype": "Section Break"}, {"fieldname": "column_break_esgd", "fieldtype": "Column Break"}, {"depends_on": "eval:([\"URL\", \"Text\"]).includes(doc.type);", "fieldname": "answer", "fieldtype": "Text Editor", "label": "Answer", "mandatory_depends_on": "eval:doc.type == \"URL\";"}, {"fieldname": "section_break_dlzh", "fieldtype": "Section Break"}, {"fieldname": "column_break_oqqy", "fieldtype": "Column Break"}, {"fieldname": "section_break_ydgh", "fieldtype": "Section Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "make_attachments_public": 1, "modified": "2025-07-14 10:24:23.526176", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Assignment Submission", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Batch Evaluator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Course Creator", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [{"color": "Green", "title": "Pass"}, {"color": "Orange", "title": "Not Graded"}, {"color": "Red", "title": "Fail"}, {"color": "Blue", "title": "Not Applicable"}], "title_field": "assignment_title"}