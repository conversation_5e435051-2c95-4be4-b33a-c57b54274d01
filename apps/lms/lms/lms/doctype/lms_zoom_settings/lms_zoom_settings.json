{"actions": [], "allow_rename": 1, "autoname": "field:account_name", "creation": "2025-05-26 13:04:18.285735", "doctype": "DocType", "engine": "InnoDB", "field_order": ["enabled", "section_break_xfow", "account_name", "member", "member_name", "member_image", "column_break_fxxg", "account_id", "client_id", "client_secret"], "fields": [{"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "account_id", "fieldtype": "Data", "in_list_view": 1, "label": "Account ID", "reqd": 1}, {"fieldname": "client_id", "fieldtype": "Data", "label": "Client ID", "reqd": 1}, {"fieldname": "client_secret", "fieldtype": "Password", "label": "Client Secret", "reqd": 1}, {"fieldname": "member", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Member", "options": "User", "reqd": 1}, {"fetch_from": "member.full_name", "fieldname": "member_name", "fieldtype": "Data", "label": "Member Name"}, {"fieldname": "section_break_xfow", "fieldtype": "Section Break"}, {"fieldname": "account_name", "fieldtype": "Data", "label": "Account Name", "reqd": 1, "unique": 1}, {"fieldname": "column_break_fxxg", "fieldtype": "Column Break"}, {"fetch_from": "member.user_image", "fieldname": "member_image", "fieldtype": "Attach Image", "label": "Member Image"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-08 12:20:48.314056", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Zoom Settings", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "Batch Evaluator", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}