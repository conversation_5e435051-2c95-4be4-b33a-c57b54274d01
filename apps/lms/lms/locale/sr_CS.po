msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-07-11 16:04+0000\n"
"PO-Revision-Date: 2025-07-16 19:54\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Serbian (Latin)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sr-CS\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: sr_CS\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr " Molimo Vas da pregledate i ocenite."

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr "% završeno"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr "<a href=\"/app/lms-settings/LMS%20Settings\">LMS podešavanja</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr "<a href=\"/app/web-page/new-web-page-1\">Postavite početnu stranicu</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr "<a href=\"/lms/courses\">Posetite LMS portal</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr "<a href=\"/lms/courses/new/edit\">Kreiraj obuku</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr "<a href=\"https://docs.frappe.io/learning\">Dokumentacija</a>"

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr "<p>Zdravo {{ member_name }},</p>\\n\\n<p>Upisani ste u našu predstojeću grupu {{ batch_name }}.</p>\\n\\n<p>Hvala Vam,</p>\\n<p>Frappe Learning</p>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr "<span class=\"h4\"><b>Započnite</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr "<span class=\"h4\"><b>Master podaci</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr "<span style=\"font-size: 18px;\"><b>Statistika</b></span>"

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr "Obuka ne može imati istovremeno plaćeni sertifikat i sertifikat o pohađanju."

#: frontend/src/pages/CourseForm.vue:88
msgid "A one line introduction to the course that appears on the course card"
msgstr "Kratak opis kursa koji se pojavljuje na kartici obuke"

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr "O"

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr "O ovoj grupi"

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr "Prihvatanje uslova i/ili pravila"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr "Prihvaćeno"

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr "ID naloga"

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: frontend/src/components/Settings/ZoomSettings.vue:192
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr "Naziv računa"

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr "Dostignuća"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr "Aktivan"

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr "Aktivni članovi"

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:93
#: frontend/src/components/Settings/Members.vue:91
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr "Dodaj"

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr "Dodaj poglavlje"

#: frontend/src/components/Settings/Evaluators.vue:91
msgid "Add Evaluator"
msgstr "Dodaj osobu za ocenjivanje"

#: frontend/src/components/CourseOutline.vue:146
msgid "Add Lesson"
msgstr "Dodaj lekciju"

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr "Dodaj kviz u video-snimak"

#: frontend/src/components/Controls/ChildTable.vue:69
msgid "Add Row"
msgstr "Dodaj red"

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr "Dodaj termin"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:35
msgid "Add Test Case"
msgstr "Dodaj test primer"

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr "Dodaj poglavlje"

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr "Dodaj lekciju"

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr "Dodaj studenta"

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a chapter"
msgstr "Dodaj poglavlje"

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr "Dodaj obuku"

#: frontend/src/pages/CourseForm.vue:69
msgid "Add a keyword and then press enter"
msgstr "Dodaj ključnu reč, a zatim pritisni enter"

#: frontend/src/components/AppSidebar.vue:569
msgid "Add a lesson"
msgstr "Dodaj lekciju"

#: frontend/src/components/Settings/Members.vue:88
msgid "Add a new member"
msgstr "Dodaj novog člana"

#: frontend/src/components/Modals/Question.vue:166
#: frontend/src/pages/QuizForm.vue:200
msgid "Add a new question"
msgstr "Dodaj novo pitanje"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:5
msgid "Add a programming exercise to your lesson"
msgstr "Dodaj vežbu programiranja u svoju lekciju"

#: frontend/src/components/AssessmentPlugin.vue:7
msgid "Add a quiz to your lesson"
msgstr "Dodajte kviz u svoju lekciju"

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr "Dodaj procenu"

#: frontend/src/components/AssessmentPlugin.vue:8
msgid "Add an assignment to your lesson"
msgstr "Dodajte zadatak u svoju lekciju"

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr "Dodajte bar jedan mogući odgovor za ovo pitanje: {0}"

#: frontend/src/components/AppSidebar.vue:532
msgid "Add courses to your batch"
msgstr "Dodajte obuke u Vašu grupu"

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr "Dodaj kviz u ovaj video-snimak"

#: frontend/src/components/AppSidebar.vue:511
msgid "Add students to your batch"
msgstr "Dodajte studente u svoju grupu"

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr "Dodajte veb-stranicu u bočnu traku"

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr "Dodajte svoj zadatak kao {0}"

#: frontend/src/components/AppSidebar.vue:444
msgid "Add your first chapter"
msgstr "Dodajte Vaše prvo poglavlje"

#: frontend/src/components/AppSidebar.vue:460
msgid "Add your first lesson"
msgstr "Dodajte Vašu prvu lekciju"

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr "Adresa"

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr "Adresa, red 1"

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr "Adresa, red 2"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr "Administrator"

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr "Sve"

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr "Sve grupe"

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr "Dodaj obuke"

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr "Sve podneseno"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr "Sva pitanja treba da imaju iste ocene ukoliko je postavljeno ograničenje."

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr "Dozvoli pristup gostima"

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr "Dozvoli oglase za posao sa veb-sajta"

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr "Dozvoli samostalni upis"

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr "Dozvoli pristup budućim datumima"

#: frontend/src/pages/BatchForm.vue:64
msgid "Allow self enrollment"
msgstr "Dozvoli samostalni upis"

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr "Već registrovan"

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:275 frontend/src/pages/CourseForm.vue:254
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr "Iznos"

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr "Iznos (USD)"

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr "Iznos i valuta su neophodni za plaćene grupe."

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr "Iznos i valuta su neophodni za plaćene sertifikate."

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr "Iznos i valuta su neophodni za plaćene obuke."

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr "Iznos sa GST"

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr "Saopštenje"

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr "Saopštenje je uspešno poslato"

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr "Saopštenje je neophodno"

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr "Odgovor"

#: frontend/src/pages/CourseForm.vue:121 frontend/src/pages/CourseForm.vue:140
msgid "Appears on the course card in the course list"
msgstr "Prikazuje se na kartici obuke u listi obuka"

#: frontend/src/pages/BatchForm.vue:250
msgid "Appears when the batch URL is shared on any online platform"
msgstr "Prikazuje se kada je URL grupe podeljen na bilo kojoj onlajn platforni"

#: frontend/src/pages/BatchForm.vue:231
msgid "Appears when the batch URL is shared on socials"
msgstr "Prikazuje se kada je URL grupe podeljen na društvenim mrežama"

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr "Primeni"

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr "Primeni GST za Indiju"

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr "Primeni zaokruživanje na ekvivalent"

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr "Prijavite se za ovaj posao"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr "Odobreno"

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr "Aplikacije"

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr "Arhivirano"

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr "Da li ste sigurni da želite da otkažete ovo ocenjivanje? Ova radnja se ne može poništiti."

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Da li ste sigurni da želite da se prijavite na svoju Frappe Cloud kontrolnu tablu?"

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr "Procena"

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr "Naziv procene"

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr "Vrsta procene"

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr "Procena je uspešno dodata"

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr "Procena {0} je već dodata ovoj grupi."

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:581
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr "Procene"

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr "Dodeli"

#: frontend/src/components/Settings/BadgeForm.vue:28
msgid "Assign For"
msgstr "Dodeli za"

#: frontend/src/components/Settings/BadgeForm.vue:58
msgid "Assign To"
msgstr "Dodeli"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:7
msgid "Assign a Badge"
msgstr "Dodeli bedž"

#: frontend/src/components/Settings/Badges.vue:221
msgid "Assigned For"
msgstr "Dodeljeno za"

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/components/Assessments.vue:245
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr "Zadatak"

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr "Prilog zadatka"

#: frontend/src/components/Settings/BadgeForm.vue:198
#: frontend/src/components/Settings/Badges.vue:204
msgid "Assignment Submission"
msgstr "Podnošenje zadatka"

#: frontend/src/pages/AssignmentSubmissionList.vue:222
msgid "Assignment Submissions"
msgstr "Podnošenja zadataka"

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr "Naslov zadatka"

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr "Zadatak je uspešno kreiran"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr "Zadaci za lekciju {0} od strane {1} već postoje."

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr "Zadatak je uspešno podnet"

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr "Zadatak je uspešno ažuriran"

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr "Zadatak će se prikazivati na dnu u okviru lekcije."

#: frontend/src/components/AppSidebar.vue:585
#: frontend/src/components/Settings/Badges.vue:163
#: frontend/src/pages/Assignments.vue:208 lms/www/lms.py:273
msgid "Assignments"
msgstr "Dodeljeni zadaci"

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr "Bar jedna opcija mora biti tačna za ovo pitanje."

#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.py:15
msgid "At least one test case is required for the programming exercise."
msgstr "Najmanje jedan test primer je neophodan za vežbu programiranja."

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr "Prisustvo na predavanju - {0}"

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr "Prisustvovano u trajanju od"

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr "Prisutni"

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr "Preferencija odevanja"

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr "Odobri pristup Google Calendar-u"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr "Automatska dodela"

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr "Automatsko snimanje"

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr "Dostupnost je uspešno ažurirana"

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr "Prosečna ocena povratnih informacija"

#: frontend/src/components/Modals/CourseProgressSummary.vue:104
msgid "Average Progress %"
msgstr "Prosečan napredak %"

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr "Prosečna ocena"

#: frontend/src/components/Modals/VideoStatistics.vue:65
msgid "Average Watch Time (seconds)"
msgstr "Prosečno vreme gledanja (u sekundama)"

#: frontend/src/pages/Lesson.vue:151
msgid "Back to Course"
msgstr "Nazad na obuku"

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:32
#: frontend/src/components/Settings/Badges.vue:214
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr "Bedž"

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr "Opis bedža"

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr "Slika bedža"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:131
msgid "Badge assignment created successfully"
msgstr "Dodela bedža je uspešno kreirana"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:112
msgid "Badge assignment updated successfully"
msgstr "Dodela bedža je uspešno ažurirana"

#: frontend/src/components/Settings/BadgeAssignments.vue:173
msgid "Badge assignments deleted successfully"
msgstr "Dodele bedževa su uspešno obrisane"

#: frontend/src/components/Settings/BadgeForm.vue:182
msgid "Badge created successfully"
msgstr "Bedž je uspešno kreiran"

#: frontend/src/components/Settings/Badges.vue:190
msgid "Badge deleted successfully"
msgstr "Bedž je uspešno obrisan"

#: frontend/src/components/Settings/BadgeForm.vue:162
msgid "Badge updated successfully"
msgstr "Bedž je uspešno ažuriran"

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: frontend/src/components/Settings/BadgeForm.vue:195
#: frontend/src/components/Settings/Badges.vue:200
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr "Grupa"

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr "Šablon potvrde grupe"

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr "Grupna obuka"

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr "Opis grupe"

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:133
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr "Detalji grupe"

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr "Neobrađeni detalji grupe"

#: frontend/src/components/Settings/BadgeForm.vue:204
#: frontend/src/components/Settings/Badges.vue:202
msgid "Batch Enrollment"
msgstr "Upis u grupu"

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr "Potvrda upisa u grupu"

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr "Osoba za ocenjivanje grupe"

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr "Naziv grupe"

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr "Stara grupa"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr "Podešavanja grupe"

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr "Datum početka grupe:"

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr "Rezime grupe"

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr "Naslov grupe"

#: frontend/src/pages/BatchForm.vue:578
msgid "Batch deleted successfully"
msgstr "Grupa je uspešno obrisana"

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr "Datum završetka grupe ne može biti pre datuma početka grupe"

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr "Grupa je već započela."

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr "Grupa je rasprodata."

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr "Vreme početka grupe ne može biti veće ili jednako vremenu završetka grupe."

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr "Grupa:"

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr "Grupe"

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr "Datum početka"

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr "Srdačan pozdrav"

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr "Detalji fakturisanja"

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr "Naziv za fakturisanje"

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr "Biografija"

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr "Sadržaj"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr "I samostalan i timski rad"

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr "Organizaciona jedinica"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr "Vlasnik biznisa"

#: frontend/src/components/CourseCardOverlay.vue:54
msgid "Buy this course"
msgstr "Kupite ovu obuku"

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr "Od strane"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr "CGPA/4"

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr "Otkaži"

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr "Otkažite ocenjivanje?"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr "Otkazano"

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr "Detalji o karijernim preferencijama"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr "Ležerna odeća"

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:199 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr "Kategorija"

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr "Naziv kategorije"

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr "Kategorija je uspešno dodata"

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr "Kategorija je uspešno obrisana"

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr "Kategorija je uspešno ažurirana"

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr "Sertifikat"

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr "Šablon imejla za sertifikat"

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr "Link sertifikata"

#: frontend/src/components/CourseCardOverlay.vue:156
msgid "Certificate of Completion"
msgstr "Sertifikat o pohađanju"

#: frontend/src/components/Modals/Event.vue:317
msgid "Certificate saved successfully"
msgstr "Sertifikat je uspešno sačuvan"

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr "Sertifikati"

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr "Sertifikati su uspešno generisani"

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:589
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:381
#: frontend/src/pages/BatchForm.vue:69 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr "Sertifikacija"

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr "Detalji sertifikacije"

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr "Naziv sertifiakcije"

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr "Sertifikovan"

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr "Sertifikovani članovi"

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr "Sertifikovani učesnici"

#: lms/templates/assignment.html:13
msgid "Change"
msgstr "Promena"

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr "Promene su uspešno sačuvane"

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of the chapter (Link) field in DocType 'LMS Video Watch Duration'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr "Poglavlje"

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr "Referenca poglavlja"

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr "Poglavlje je uspešno dodato"

#: frontend/src/components/CourseOutline.vue:337
msgid "Chapter deleted successfully"
msgstr "Poglavlje je uspešno obrisano"

#: frontend/src/components/CourseOutline.vue:271
msgid "Chapter moved successfully"
msgstr "Poglavlje je uspešno premešteno"

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr "Poglavlje je uspešno ažurirano"

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr "Poglavlja"

#: frontend/src/components/Quiz.vue:229 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr "Označi"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:16
msgid "Check All Submissions"
msgstr "Proveri sve podneske"

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr "Proveri diskusiju"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:97
msgid "Check Submission"
msgstr "Proveri podnesak"

#: frontend/src/components/Modals/AssignmentForm.vue:55
#: frontend/src/pages/QuizForm.vue:39
msgid "Check Submissions"
msgstr "Proveri podneske"

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr "Pogledaj {0} za više informacija o sertifikaciji."

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr "Završi kupovinu kurseva"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr "Izbori"

#: frontend/src/components/Quiz.vue:644 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr "Izaberi sve tačne odgovore"

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr "Izaberi postojeće pitanje"

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr "Izaberi ikonicu"

#: frontend/src/components/Quiz.vue:645 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr "Izaberite jedan odgovor"

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr "Grad"

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr "Predavanje:"

#: frontend/src/components/Controls/Link.vue:50
msgid "Clear"
msgstr "Očisti"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr "Jasno definisana uloga"

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr "Kliknite ovde"

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr "ID klijenta"

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr "Tajna klijenta"

#: frontend/src/components/Settings/Categories.vue:27
msgid "Close"
msgstr "Zatvori"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr "Zatvoreno"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr "Oblak"

#. Label of the code (Code) field in DocType 'LMS Exercise'
#. Label of the code (Code) field in DocType 'LMS Programming Exercise
#. Submission'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Code"
msgstr "Kod"

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr "Obrazovna grupa"

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr "Zahtev za pridruživanjem obrazovnoj grupi"

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr "Član obrazovne grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr "Osoblje obrazovne grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr "Podgrupa obrazovne grupe"

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr "Veb-stranica obrazovne grupe"

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr "Preferencije za saradnju"

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr "Sažmi"

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr "Naziv fakulteta"

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr "Boja"

#: frontend/src/pages/BatchForm.vue:303 frontend/src/pages/CourseForm.vue:292
msgid "Comma separated keywords for SEO"
msgstr "Ključne reči, odvojene zarezom, za SEO"

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr "Komentari"

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr "Komentari od osobe za ocenjivanje"

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr "Zajedničke ključne reči koje će se koristiti za sve stranice"

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr "Kompanija"

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr "Detalji kompanije"

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr "Imejl adresa kompanije"

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr "Logo kompanije"

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr "Naziv kompanije"

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr "Vrsta kompanije"

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr "Veb-sajt kompanije"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:69
msgid "Compiler Message"
msgstr "Poruka kompajlera"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr "Završeno"

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr "Završite registraciju"

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr "Dovršite svoj upis"

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr "Dovršite svoj upis - Nemojte ga propustiti!"

#: frontend/src/components/VideoBlock.vue:144
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr "Završite predstojeći kviz da biste nastavili gledanje video-snimka. Kviz će biti dostupan za {0} {1}."

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr "Završeno"

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:241
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr "Sertifikat o završetku"

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/BadgeForm.vue:65
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr "Uslov"

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr "Uslov mora biti u važećem JSON formatu."

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr "Uslov mora biti važeći python kod."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr "Sprovesti ocenjivanje"

#: frontend/src/pages/BatchForm.vue:148
msgid "Configurations"
msgstr "Konfiguracije"

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr "Potvrdi"

#: frontend/src/pages/BatchForm.vue:556
msgid "Confirm your action to delete"
msgstr "Potvrdite svoju radnju za brisanje"

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr "Potvrdni imejl je poslat"

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr "Šablon imejla za potvrdu"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr "Čestitamo na dobijanju sertifikata!"

#: frontend/src/components/CourseCardOverlay.vue:63
#: frontend/src/pages/Lesson.vue:53
msgid "Contact the Administrator to enroll for this course."
msgstr "Kontaktirajte administratora da biste se upisali na ovu obuku."

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr "Sadržaj"

#: frontend/src/components/CourseCardOverlay.vue:33
msgid "Continue Learning"
msgstr "Nastavite sa učenjem"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr "Ugovor"

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr "Politika kolačića"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr "Korporativna organizacija"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:189
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr "Tačno"

#: frontend/src/components/Modals/Question.vue:79
msgid "Correct Answer"
msgstr "Tačan odgovor"

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr "Država"

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: frontend/src/components/Settings/BadgeForm.vue:194
#: frontend/src/components/Settings/Badges.vue:199
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr "Obuka"

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr "Poglavlje obuke"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr "Obuka je završena"

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr "Završene obuke"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Course Creator"
msgstr "Autor obuke"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr "Podaci o obuci"

#: frontend/src/pages/CourseForm.vue:190
msgid "Course Description"
msgstr "Opis obuke"

#: frontend/src/components/Settings/BadgeForm.vue:203
#: frontend/src/components/Settings/Badges.vue:201
msgid "Course Enrollment"
msgstr "Upis na kurs"

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr "Upis na obuku"

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr "Osoba za ocenjivanje obuke"

#: frontend/src/pages/CourseForm.vue:96
msgid "Course Image"
msgstr "Slika obuke"

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr "Predavač na obuci"

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr "Lekcija obuke"

#: lms/www/lms.py:87
msgid "Course List"
msgstr "Lista obuke"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr "Naziv obuke"

#: frontend/src/pages/CourseDetail.vue:78 frontend/src/pages/CourseForm.vue:302
msgid "Course Outline"
msgstr "Plan obuke"

#. Name of a report
#: frontend/src/components/Modals/CourseProgressSummary.vue:5
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr "Sažetak o napretku obuke"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr "Podešavanje obuke"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr "Statistika obuke"

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr "Naslov obuke"

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr "Obuka je dodata u program"

#: frontend/src/pages/CourseForm.vue:537
msgid "Course created successfully"
msgstr "Obuka je uspešno kreirana"

#: frontend/src/pages/CourseForm.vue:574
msgid "Course deleted successfully"
msgstr "Obuka je uspešno obrisana"

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr "Obuka je uspešno premeštena"

#: frontend/src/pages/CourseForm.vue:557
msgid "Course updated successfully"
msgstr "Obuka je uspešno ažurirana"

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr "Obuka {0} je već dodata u ovu grupu."

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr "Obuke"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr "Završene obuke"

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr "Obuke su uspešno obrisane"

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr "Naslovna slika"

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:32
#: frontend/src/pages/Programs.vue:93 frontend/src/pages/Quizzes.vue:10
msgid "Create"
msgstr "Kreiraj"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr "Kreiraj sertifikat"

#: frontend/src/components/Controls/Link.vue:38
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr "Kreiraj novi"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:7
msgid "Create Programming Exercise"
msgstr "Kreiraj vežbu programiranja"

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr "Kreiraj obuku"

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr "Kreiraj onlajn predavanje"

#: frontend/src/pages/Quizzes.vue:101
msgid "Create a Quiz"
msgstr "Kreiraj kviz"

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a batch"
msgstr "Kreiraj grupu"

#: frontend/src/components/AppSidebar.vue:567
msgid "Create a course"
msgstr "Kreiraj obuku"

#: frontend/src/components/AppSidebar.vue:577
msgid "Create a live class"
msgstr "Kreiraj onlajn predavanje"

#: frontend/src/components/Settings/BadgeForm.vue:5
msgid "Create a new Badge"
msgstr "Kreiraj novi bedž"

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr "Kreiraj zadatak"

#: frontend/src/components/AppSidebar.vue:501
msgid "Create your first batch"
msgstr "Kreirajte svoju prvu grupu"

#: frontend/src/components/AppSidebar.vue:432
msgid "Create your first course"
msgstr "Kreirajte svoju prvu obuku"

#: frontend/src/components/AppSidebar.vue:479
msgid "Create your first quiz"
msgstr "Kreirajte svoj prvi kviz"

#: frontend/src/pages/Assignments.vue:173 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr "Kreirano"

#: frontend/src/components/AppSidebar.vue:573
msgid "Creating a batch"
msgstr "Kreiranje grupe"

#: frontend/src/components/AppSidebar.vue:564
msgid "Creating a course"
msgstr "Kreiranje obuke"

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:282 frontend/src/pages/CourseForm.vue:271
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr "Valuta"

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr "Trenutna lekcija"

#: frontend/src/components/AppSidebar.vue:595
msgid "Custom Certificate Templates"
msgstr "Prilagođeni šablon sertifikata"

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr "Prilagođeni HTML"

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr "Prilagođena skripta (JavaScript)"

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr "Prilagođeni sadržaj za prijavu"

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr "Prilagođavanja"

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr "Kontrolna tabla"

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr "Datum"

#: frontend/src/pages/BatchForm.vue:76
msgid "Date and Time"
msgstr "Vreme i datum"

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr "Datum:"

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr "Dan"

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr "Zdravo"

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr "Zdravo "

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr "Zdravo {{ member_name }},\\n\\nUpisani ste u našu predstojeću grupu {{ batch_name }}.\\n\\nHvala Vam,\\nFrappe Learning"

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr "Podrazumevana valuta"

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr "Vrsta diplome"

#: frontend/src/components/Controls/ChildTable.vue:56
#: frontend/src/components/CourseOutline.vue:283
#: frontend/src/components/CourseOutline.vue:349
#: frontend/src/components/Settings/Badges.vue:171
#: frontend/src/pages/BatchForm.vue:562 frontend/src/pages/CourseForm.vue:587
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:67
msgid "Delete"
msgstr "Obriši"

#: frontend/src/components/CourseOutline.vue:67
msgid "Delete Chapter"
msgstr "Obriši poglavlje"

#: frontend/src/pages/CourseForm.vue:581
msgid "Delete Course"
msgstr "Obriši obuku"

#: frontend/src/components/CourseOutline.vue:343
msgid "Delete this chapter?"
msgstr "Obriši ovo poglavlje?"

#: frontend/src/components/CourseOutline.vue:277
msgid "Delete this lesson?"
msgstr "Obrišite ovu lekciju?"

#: frontend/src/pages/CourseForm.vue:582
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr "Brisanjem obuke takođe će se obrisati sva poglavlja i lekcije. Da li ste sigurni da želite da obrišete ovu obuku?"

#: frontend/src/pages/BatchForm.vue:557
msgid "Deleting this batch will also delete all its data including enrolled students, linked courses, assessments, feedback and discussions. Are you sure you want to continue?"
msgstr "Brisanjem ove grupe takođe će biti obrisani svi njeni podaci, uključujući upisane studente, povezane kurseve, procene, povratne informacije i diskusije. Da li ste sigurni da želite da nastavite?"

#: frontend/src/components/CourseOutline.vue:344
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Brisanjem ovog poglavlja takođe će se obrisati sve lekcije, a poglavlje će trajno biti uklonjeno iz obuke. Ova radnja se ne može poništiti. Da li ste sigurni da želite da nastavite?"

#: frontend/src/components/CourseOutline.vue:278
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Brisanjem ove lekcije ona će trajno biti uklonjena iz obuke. Ova radnja se ne može poništiti. Da li ste sigurni da želite da nastavite?"

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/components/Settings/BadgeForm.vue:32
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr "Opis"

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr "Radna površina"

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:21 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:50
msgid "Details"
msgstr "Detalji"

#: frontend/src/pages/CourseForm.vue:181
msgid "Disable Self Enrollment"
msgstr "Onemogući samostalni upis"

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr "Onemogući samostalno učenje"

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr "Onemogući registraciju"

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/Badges.vue:56
#: frontend/src/components/Settings/ZoomSettings.vue:66
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr "Onemogućeno"

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr "Odbaci"

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr "Diskusija"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr "Dokument"

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr "Nemojte propustiti priliku da unapredite svoje znanje. Kliknite ispod da biste završili svoj upis"

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr "Poželjne kompanije"

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr "Pronađene su duplikat opcije za ovo pitanje."

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr "Trajanje"

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr "Trajanje (u minutima)"

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr "Trajanje onlajn predavanja u minutima"

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr "Imejl"

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr "Imejl"

#: frontend/src/components/BatchOverlay.vue:129
#: frontend/src/components/CourseCardOverlay.vue:116
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/components/Settings/Badges.vue:156
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:130
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr "Uredi"

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr "Uredi zadatak"

#: frontend/src/components/Settings/BadgeForm.vue:5
msgid "Edit Badge"
msgstr "Uredi bedž"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:8
msgid "Edit Badge Assignment"
msgstr "Uredi dodelu bedža"

#: frontend/src/components/CourseOutline.vue:60
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr "Uredi poglavlje"

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr "Uredi imejl šablon"

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr "Uredi profil"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:8
msgid "Edit Programming Exercise"
msgstr "Uredi vežbu programiranja"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr "Uredi Zoom nalog"

#: frontend/src/pages/QuizForm.vue:199
msgid "Edit the question"
msgstr "Uredi pitanje"

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr "Obrazovanje"

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr "Detalji obrazovanja"

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr "Detalji obrazovanja"

#: frontend/src/components/Settings/Evaluators.vue:105
#: frontend/src/components/Settings/Members.vue:103
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr "Imejl"

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr "ID imejla"

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr "Imejl poslat"

#: frontend/src/pages/BatchForm.vue:161
msgid "Email Template"
msgstr "Imejl šablon"

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr "Imejl šablon je uspešno kreiran"

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr "Imejl šablon je uspešno ažuriran"

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr "Imejl šabloni"

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Email Templates deleted successfully"
msgstr "Imejl šablon je uspešno obrisan"

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr "Imejlovi"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr "Zaposleno lice"

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr "Omogući"

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr "Omogućite Google API u Google podešavanjima za slanje pozivnica za ocenjivanje u kalendar."

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr "Omogući putanju učenja"

#. Label of the enable_negative_marking (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Enable Negative Marking"
msgstr "Omogući negativno ocenjivanje"

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr "Omogućite ovo samo ukoliko želite da otpremite SCORM paket kao poglavlje."

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/BadgeForm.vue:14
#: frontend/src/components/Settings/Badges.vue:53
#: frontend/src/components/Settings/ZoomSettings.vue:63
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr "Omogućeno"

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr "Omogućavanjem ovoga sertifikat će biti objavljen na stranici sa sertifikovanim učesnicima."

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:89 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr "Datum završetka"

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr "Datum završetka (ili očekivani)"

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:105
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr "Vreme završetka"

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr "Završeno"

#: frontend/src/components/BatchOverlay.vue:113
msgid "Enroll Now"
msgstr "Upišite se sada"

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr "Upisan"

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:138
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr "Upisani studenti"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr "Potvrda o upisu za {0}"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr "Broj upisanih"

#: lms/lms/utils.py:1943
msgid "Enrollment Failed"
msgstr "Upis je neuspešan"

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/CourseProgressSummary.vue:97
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr "Upisi"

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr "Unesite ID klijenta i klijentsku tajnu u Google podešavanjima da biste slali pozivnice za ocenjivanje u kalendar."

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr "Unesite URL"

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr "Unesite tačan odgovor"

#: frontend/src/components/Modals/ZoomAccountModal.vue:169
msgid "Error creating Zoom Account"
msgstr "Greška prilikom kreiranja Zoom naloga"

#: frontend/src/components/Settings/BadgeForm.vue:186
msgid "Error creating badge"
msgstr "Greška prilikom kreiranja bedža"

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr "Greška prilikom generisanja imejl šablona"

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr "Greška prilikom kreiranja onlajn predavanja. Molimo Vas da pokušate ponovo. {0}"

#: frontend/src/pages/Quizzes.vue:212
msgid "Error creating quiz: {0}"
msgstr "Greška prilikom kreiranja kviza: {0}"

#: frontend/src/components/Settings/Badges.vue:193
msgid "Error deleting badge"
msgstr "Greška prilikom brisanja bedža"

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:179
msgid "Error deleting email templates"
msgstr "Greška prilikom brisanja imejl šablona"

#: frontend/src/components/Modals/ZoomAccountModal.vue:207
msgid "Error updating Zoom Account"
msgstr "Greška prilikom ažuriranja Zoom naloga"

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr "Greška prilikom ažuriranja imejl šablona"

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:374 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr "Ocenjivanje"

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr "Detalji ocenjivanja"

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:122
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr "Datum završetka ocenjivanja"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr "Zahtev za ocenjivanje"

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr "Datum završetka ocenjivanje ne može biti manji od datuma završetka grupe."

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr "Ocenjivanje je uspešno sačuvano"

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:260 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr "Osoba za ocenjivanje"

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr "Ime osobe za ocenjivanje"

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr "Raspored osobe za ocenjivanje"

#: frontend/src/components/Settings/Evaluators.vue:163
msgid "Evaluator added successfully"
msgstr "Osoba za ocenjivanje je uspešno dodata"

#: frontend/src/components/Settings/Evaluators.vue:196
msgid "Evaluator deleted successfully"
msgstr "Osoba za ocenjivanje je uspešno obrisana"

#: lms/lms/api.py:1463
msgid "Evaluator does not exist."
msgstr "Osoba za ocenjivanje ne postoji."

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr "Osoba za ocenjivanje je obavezna za plaćene sertifikate."

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Settings/BadgeForm.vue:51
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr "Događaj"

#: frontend/src/pages/BatchForm.vue:116
msgid "Example: IST (+5:30)"
msgstr "Primer: IST (+5:30)"

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#. Label of the exercise (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:274
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise"
msgstr "Vežba"

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr "Najnoviji rad na vežbi"

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr "Podnošenje vežbe"

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#. Label of the exercise_title (Data) field in DocType 'LMS Programming
#. Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise Title"
msgstr "Naslov vežbe"

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr "Proširi"

#. Label of the expected_output (Data) field in DocType 'LMS Test Case'
#. Label of the expected_output (Data) field in DocType 'LMS Test Case
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:127
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Expected Output"
msgstr "Očekivani izlaz"

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr "Datum isteka"

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr "Datum isteka"

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:75
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr "Objašnjenje"

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr "Objašnjenje "

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr "Istražite više"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:366
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr "Neuspeh"

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:37
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Failed"
msgstr "Neuspešno"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:136
msgid "Failed to create badge assignment: "
msgstr "Neuspešno kreiranje dodele bedža: "

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr "Neuspešno preuzimanje podataka o prisustvu za Zoom za predavanje {0}: {1}"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:358
msgid "Failed to submit. Please try again. {0}"
msgstr "Neuspešno podnošenje. Pokušajte ponovo. {0}"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:117
msgid "Failed to update badge assignment: "
msgstr "Neuspešno ažuriranje dodele bedža: "

#: frontend/src/utils/index.js:671
msgid "Failed to update meta tags {0}"
msgstr "Neuspešno ažuriranje meta oznaka {0}"

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:176
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr "Istaknuto"

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr "Povratna informacija"

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr "Budite slobodni da izmenite svoj unos ukoliko je to neophodno."

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr "Polja za proveru"

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr "Oblast studija"

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr "Vrsta fajla"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:23
msgid "Filter by Exercise"
msgstr "Filtriraj po vežbi"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:28
msgid "Filter by Member"
msgstr "Filtriraj po članu"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:39
msgid "Filter by Status"
msgstr "Filtriraj po statusu"

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:110
msgid "First Name"
msgstr "Ime"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr "Fiksno vreme od 9,00 do 17,00"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr "Fleksibilno vreme"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr "Formalna odeća"

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr "Besplatno"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr "Freelance"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr "Freelance / Samo razgledam"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr "Francuski sistem (npr. Izuzetno)"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr "Petak"

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr "Od"

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr "Datum početka"

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr "Ime i prezime"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr "Puno radno vreme"

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr "Funkcija"

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr "GST iznos"

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr "GST broj"

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr "GSTIN"

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr "Opšte"

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr "Generiši sertifikate"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr "Generiši Google Meet Link"

#: frontend/src/components/CourseCardOverlay.vue:89
msgid "Get Certificate"
msgstr "Preuzmi sertifikat"

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr "Stekni sertifikat"

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr "Započni"

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr "GitHub ID"

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr "Google Meet Link"

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr "Ocena"

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr "Ocena zadatka"

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr "Vrsta ocene"

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr "Ocenjivanje"

#: frontend/src/components/Settings/BadgeForm.vue:46
#: frontend/src/components/Settings/Badges.vue:235
msgid "Grant Only Once"
msgstr "Dodeli samo jednom"

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr "Dodeli samo jednom"

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr "Imate nalog? Prijavite se"

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr "Naslov"

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr "Zdravo"

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr "Pomoć"

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr "Pomozite drugima da nauče nešto novo kreiranjem obuke."

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr "Pomozite nam da se poboljšamo dajući nam svoje utiske."

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr "Pomozite nam da unapredimo materijale za obuku."

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr "Pomozite nam da razumemo Vaše potrebe"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr "Zdravo {0}"

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr "Zdravo,"

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr "Zdravo"

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr "Zdravo {0},"

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr "Zdravo,"

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr "Sakrij moje privatne informacije od drugih"

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr "Saveti"

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr "Domaćin"

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr "Trenutno radim ovde"

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr "Sa zadovoljstvom Vas obaveštavamo da ste uspešno stekli sertifikat za obuku {0}. Čestitamo!"

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr "Trenutno sam u potrazi za poslom"

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr "Nisam dostupan"

#: frontend/src/pages/QuizForm.vue:338
msgid "ID"
msgstr "ID"

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr "Ikonica"

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr "Odredi kategoriju korisnika"

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr "Ukoliko je opcija uključi u pregled omogućena za lekciju, tada će lekcija biti dostupna i korisnicima koji nisu prijavljeni."

#: frontend/src/components/Quiz.vue:46
msgid "If you answer incorrectly, {0} {1} will be deducted from your score for each incorrect answer."
msgstr "Ukoliko odgovorite netačno, {0} {1} biće oduzeto od Vašeg rezultata za svaki netačan odgovor."

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr "Ukoliko više niste zainteresovani da budete mentor obuke"

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr "Ukoliko to ne učinite samostalno, kviz će biti automatski podnet kada istekne vreme."

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr "Ukoliko imate pitanja ili Vam je potrebna pomoć, slobodno kontaktirajte naš tim podrške."

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr "Ukoliko imate pitanja ili Vam je potrebna pomoć, slobodno nas kontaktirajte."

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr "Ukoliko ovde unesete iznos, podešavanje ekvivalenta u američkim dolarima (USD) neće biti primenjeno."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:66
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr "Ukoliko želite pitanja sa otvorenim odgovorima, proverite da li je svako pitanje u kvizu vrste otvoreni odgovor."

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr "Slika"

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr "Pretraga slika omogućena uz podršku"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:232
msgid "Image: Corrupted Data Stream"
msgstr "Slika: Oštećen tok podataka"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:358
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr "U toku"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr "Neaktivan"

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr "Uključi u pregled"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr "Nepotpuno"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:194
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr "Netačno"

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr "Indeks"

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr "Oznaka indeksa"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr "Samostalni rad"

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr "Industrija"

#. Label of the input (Data) field in DocType 'LMS Test Case'
#. Label of the input (Data) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:113
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Input"
msgstr "Ulaz"

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr "Naziv institucije"

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr "Predavač"

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr "Sadržaj predavača"

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:184 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr "Beleške predavača"

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:34 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr "Predavači"

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr "Komentari predavača"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr "Interesovanje"

#: frontend/src/components/AppSidebar.vue:556
#: frontend/src/components/AppSidebar.vue:559
msgid "Introduction"
msgstr "Uvod"

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr "Neispravan kod za poziv."

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr "Nevažeći ID kviza"

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr "Nevažeći ID kviza u sadržaju"

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr "Šifra pozivnice"

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr "Imejl pozivnica"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr "Samo za pozvane"

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr "Zahtev za pozivnicu"

#: frontend/src/components/AppSidebar.vue:490
msgid "Invite your team and students"
msgstr "Pozovite svoj tim i studente"

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr "Tačno"

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr "SCORM paket"

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr "Datum izdavanja"

#: frontend/src/components/AppSidebar.vue:592
msgid "Issue a Certificate"
msgstr "Izdaj sertifikat"

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:37
#: frontend/src/components/Settings/BadgeAssignments.vue:185
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr "Izdato"

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr "Izdato"

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr "Stavke u bočnoj traci"

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr "Stavke su uspešno uklonjene"

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr "Petar Petrović"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "JavaScript"
msgstr "JavaScript"

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr "Posao"

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr "Podnaslov table oglasa za posao"

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr "Naslov table oglasa za posao"

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr "Detalji posla"

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr "Oglasi za posao"

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr "Prilika za posao"

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr "Podešavanje posla"

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr "Naziv radnog mesta"

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr "Poslovi"

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr "Pridruži se"

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr "Pridrži se pozivu"

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr "Pridruži se sastanku"

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr "URL za pridruživanje"

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr "Pristup u"

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr "Pristup ostvaren u"

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr "LMS"

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr "LMS procena"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr "LMS zadatak"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr "LMS podnošenje zadatka"

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr "LMS bedž"

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr "LMS dodela bedža"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr "LMS grupa"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr "LMS upis u grupu"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr "LMS povratna informacija grupe"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr "LMS stara grupa"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr "LMS raspored nastave grupe"

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr "LMS kategorija"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr "LMS sertifikat"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr "LMS ocenjivanje sertifikata"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr "LMS zahtev za sertifikatom"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr "LMS obuka"

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr "LMS interesovanje za obuku"

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr "LMS povezivanje mentora na obuku"

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr "LMS napredak na obuci"

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr "LMS recenzija obuke"

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr "LMS upis"

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr "LMS vežba"

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr "LMS prijava za posao"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr "LMS onlajn predavanje"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr "LMS učesnik onlajn predavanja"

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr "LMS zahtev za mentora"

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr "LMS opcija"

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr "LMS uplata"

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr "LMS program"

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr "LMS program obuke"

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr "LMS član programa"

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "LMS Programming Exercise"
msgstr "LMS vežba programiranja"

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "LMS Programming Exercise Submission"
msgstr "LMS podnesak vežbe programiranja"

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr "LMS pitanje"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr "LMS kviz"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr "LMS pitanje u kvizu"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr "LMS rezultati kviza"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr "LMS podnošenje kviza"

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr "LMS podešavanja"

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr "LMS stavka bočne trake"

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr "LMS izvor"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr "LMS student"

#. Name of a DocType
#: lms/lms/doctype/lms_test_case/lms_test_case.json
msgid "LMS Test Case"
msgstr "LMS test primer"

#. Name of a DocType
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "LMS Test Case Submission"
msgstr "LMS podnesak test primera"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr "LMS legenda rasporeda nastave"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr "LMS šablon rasporeda nastave"

#. Name of a DocType
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "LMS Video Watch Duration"
msgstr "Trajanje gledanja LMS video-snimka"

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr "LMS Zoom podešavanja"

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr "Oznaka"

#. Label of the language (Select) field in DocType 'LMS Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:22
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Language"
msgstr "Jezik"

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr "Prezime"

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr "Poslednji podnesak"

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr "Pokreni fajl"

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr "Napušteno u"

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr "Odlazak u"

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr "Lekcija"

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr "Referenca lekcije"

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr "Naslov lekcije"

#: frontend/src/pages/LessonForm.vue:426
msgid "Lesson created successfully"
msgstr "Lekcija je uspešno kreirana"

#: frontend/src/components/CourseOutline.vue:242
msgid "Lesson deleted successfully"
msgstr "Lekcija je uspešno obrisana"

#: frontend/src/components/CourseOutline.vue:257
msgid "Lesson moved successfully"
msgstr "Lekcija je uspešno premeštena"

#: frontend/src/pages/LessonForm.vue:450
msgid "Lesson updated successfully"
msgstr "Lekcija je uspešno ažurirana"

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:131
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr "Lekcije"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr "Lekcije završene"

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr "Počnimo sa podešavanjem sadržaja na LMS-u kako biste uštedeli vreme i fokusirali se na rast."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr "Slovna ocena (npr. A, B-)"

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:110 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr "Ograniči pitanja na"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr "Ograničenje ne može biti veće ili jednako broju pitanja u kvizu."

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr "LinkedIn"

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr "LinkedIn ID"

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr "Linkovi"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr "Aktivno"

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr "Onlajn predavanje"

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr "LiveCode URL"

#: frontend/src/components/Modals/CourseProgressSummary.vue:87
#: frontend/src/components/Settings/Evaluators.vue:81
#: frontend/src/components/Settings/Members.vue:79
#: frontend/src/pages/Assignments.vue:66 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:129
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:87
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:94
msgid "Load More"
msgstr "Učitaj više"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr "Lokalno"

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr "Lokacija"

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr "Preferencija lokacije"

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:59
msgid "Login"
msgstr "Prijava"

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr "Prijava na Frappe Cloud?"

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr "Prijavite se da biste nastavili"

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr "Radujemo se Vašem upisu!"

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr "Postavi LMS kao podrazumevanu početnu stranicu"

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr "Napravi saopštenje"

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr "Unesite tačan naziv za fakturisanje jer će biti korišćeno na Vašoj fakturi."

#: frontend/src/components/BatchOverlay.vue:73
msgid "Manage Batch"
msgstr "Upravljaj grupom"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr "Menadžer"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr "Menadžer (prodaja/marketing/korisnici)"

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr "Manifest fajl"

#: frontend/src/components/Quiz.vue:120
msgid "Mark"
msgstr "Poen"

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr "Označi sve kao pročitano"

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:106
#: frontend/src/components/Quiz.vue:120 frontend/src/pages/QuizForm.vue:348
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr "Poeni"

#. Label of the marks_to_cut (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Marks To Cut"
msgstr "Poeni za oduzimanje"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr "Broj poena za pitanje broj {0} ne može biti veći od poena dodeljenih za to pitanje."

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr "Poeni od ukupno"

#: frontend/src/pages/QuizForm.vue:122
msgid "Marks to Cut"
msgstr "Poeni za oduzimanje"

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/Quizzes.vue:249 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr "Maksimalan broj pokušaja"

#: frontend/src/pages/QuizForm.vue:62
msgid "Maximum Attempts"
msgstr "Maksimalan broj pokušaja"

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:194
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr "Sredstvo"

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr "ID sredstva"

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr "Sredstvo:"

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr "ID sastanka"

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/CourseProgressSummary.vue:216
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/VideoStatistics.vue:22
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:26
#: frontend/src/components/Settings/BadgeAssignments.vue:179
#: frontend/src/components/Settings/BadgeForm.vue:215
#: frontend/src/components/Settings/ZoomSettings.vue:187
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:268
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr "Član"

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr "Član obrazovne grupe"

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr "Imejl člana"

#. Label of the member_image (Attach Image) field in DocType 'LMS Badge
#. Assignment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Enrollment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_image (Attach) field in DocType 'LMS Programming
#. Exercise Submission'
#. Label of the member_image (Attach Image) field in DocType 'LMS Video Watch
#. Duration'
#. Label of the member_image (Attach Image) field in DocType 'LMS Zoom
#. Settings'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Member Image"
msgstr "Slika člana"

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Video Watch Duration'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr "Ime člana"

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr "Podgrupa člana"

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr "Vrsta člana"

#. Label of the member_username (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_username (Data) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Username"
msgstr "Korisničko ime člana"

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr "Član je dodata u program"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr "Član se već upisao u ovu grupu"

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr "Član {0} je već dodat u ovu grupu."

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr "Članovi"

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr "Članstvo"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr "Mentor"

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr "Ime mentora"

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr "Zahtev za mentora"

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr "Šablon za kreiranje zahteva za mentora"

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr "Šablon za ažuriranje statusa zahteva za mentora"

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:294 frontend/src/pages/CourseForm.vue:283
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr "Meta opis"

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:207
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr "Meta slika"

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:300 frontend/src/pages/CourseForm.vue:289
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr "Meta ključne reči"

#: frontend/src/pages/BatchForm.vue:289 frontend/src/pages/CourseForm.vue:278
msgid "Meta Tags"
msgstr "Meta oznake"

#: lms/lms/api.py:1503
msgid "Meta tags should be a list."
msgstr "Meta oznake treba da budu lista."

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr "Ključna tačka"

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr "Neophodno su najmanje dve opcije za pitanja sa višestrukim izborom."

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr "Moderator"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:286
#: frontend/src/pages/Quizzes.vue:263
msgid "Modified"
msgstr "Izmenjeno"

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr "Izmenjeno od strane"

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr "Naziv modula je netačan ili ne postoji."

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr "Modul je neispravan."

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr "Ponedeljak"

#: frontend/src/components/AppSidebar.vue:600
msgid "Monetization"
msgstr "Monetizacija"

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr "Više"

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr "Više tačnih odgovora"

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr "Moja dostupnost"

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr "Moj kalendar"

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr "Ime"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/BadgeAssignments.vue:21
#: frontend/src/components/Settings/Badges.vue:21
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:17
#: frontend/src/components/Settings/Members.vue:17
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Courses.vue:310 frontend/src/pages/Programs.vue:14
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr "Novi"

#: lms/www/lms.py:151
msgid "New Batch"
msgstr "Nova grupa"

#: frontend/src/pages/CourseForm.vue:668 lms/www/lms.py:95
msgid "New Course"
msgstr "Nova obuka"

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr "Novi imejl šablon"

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr "Novi posao"

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr "Novi kandidat za posao"

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr "Novi program"

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr "Nova obuka u okviru programa"

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr "Novi član programa"

#: frontend/src/pages/QuizForm.vue:137
msgid "New Question"
msgstr "Novo pitanje"

#: frontend/src/pages/QuizForm.vue:404 frontend/src/pages/QuizForm.vue:412
msgid "New Quiz"
msgstr "Novi kviz"

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr "Nova registracija"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr "Novi Zoom nalog"

#: lms/lms/utils.py:609
msgid "New comment in batch {0}"
msgstr "Novi komentar u grupi {0}"

#: lms/lms/utils.py:602
msgid "New reply on the topic {0} in course {1}"
msgstr "Nova poruka na temu {0} u obuci {1}"

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr "Novi {0}"

#: frontend/src/components/Quiz.vue:237 frontend/src/pages/Lesson.vue:139
msgid "Next"
msgstr "Sledeće"

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr "Sledeće pitanje"

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr "Nema procena"

#: frontend/src/components/Settings/BadgeAssignments.vue:87
msgid "No Assignments"
msgstr "Nema zadataka"

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr "Nema obaveštenja"

#: frontend/src/components/Quiz.vue:307
msgid "No Quiz submissions found"
msgstr "Nema pronađenih podnetih kvizova"

#: frontend/src/pages/Quizzes.vue:19
msgid "No Quizzes"
msgstr "Nema kvizova"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr "Nema snimka"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:13
msgid "No Submissions"
msgstr "Nema podnesaka"

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr "Nema predstojećih ocenjivanja"

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr "Nema saopštenja"

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr "Nema sertifikata"

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr "Nema dodatih obuka"

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr "Nema kreiranih obuka"

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr "Nema obuka u ovom programu"

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr "Nema obuka u fazi pregleda"

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr "Još uvek nema primljenih povratnih informacija."

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr "Nema uvoda"

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr "Nema zakazanih onlajn predavanja"

#: frontend/src/pages/QuizForm.vue:188
msgid "No questions added yet"
msgstr "Još uvek nisu dodata pitanja"

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr "Još uvek nisu dodati kvizovi."

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr "Nema dostupnih termina za ovaj datum."

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr "Nema studenata u ovoj grupi"

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr "Nema podnesaka"

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr "Nema {0}"

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr "Br."

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr "Nije dozvoljeno"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr "Nije primenjivo"

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr "Nije pokušano"

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr "Nije dostupno za pregled"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr "Nije ocenjeno"

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr "Nije dozvoljeno"

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizForm.vue:8 frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr "Nije sačuvano"

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr "Ovde nema ničega za pregled."

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr "Obaveštenja"

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr "Obavesti me kada bude dostupno"

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr "Broj studenata"

#: frontend/src/pages/BatchForm.vue:157
msgid "Number of seats available"
msgstr "Broj dostupnih mesta"

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr "OAuth ID klijenta"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr "Kancelarija blizu kuće"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr "Uživo"

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr "Još jednom, čestitamo na ovom značajnom uspehu."

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr "Nakon što moderator oceni Vaš podnesak, ovde ćete pronaći detalje."

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr "Onlajn"

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr "Isključivo obuke kod kojih je samostalno učenje onemogućeno mogu se dodati u program."

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr "Isključivo su prihvatljivi fajlovi vrste {0}."

#: frontend/src/utils/index.js:502
msgid "Only image file is allowed."
msgstr "Dozvoljen je samo fajl slike."

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr "Dozvoljeni su samo zip fajlovi"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr "Otvoreno"

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr "Otvoreni zadatak"

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr "Otvorena obuka"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr "Otvoreno pitanje"

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:70
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr "Opcija"

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr "Opcija 1"

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr "Opcija 2"

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr "Opcija 3"

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr "Opcija 4"

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr "Opcije"

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr "ID narudžbine"

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr "Organizacija"

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr "Originalni iznos"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr "Ostali"

#. Label of the output (Data) field in DocType 'LMS Test Case Submission'
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Output"
msgstr "Rezultat"

#: frontend/src/components/Settings/BadgeForm.vue:216
#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr "Vlasnik"

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr "PAN"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr "PDF"

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr "Stranice"

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:270
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr "Plaćena grupa"

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:246
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr "Plaćeni sertifikat"

#: frontend/src/components/CourseCardOverlay.vue:165
msgid "Paid Certificate after Evaluation"
msgstr "Plaćeni sertifikat nakon ocenjivanja"

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:236
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr "Plaćena obuka"

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr "PAN broj"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr "Nepuno vreme"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr "Delimično završeno"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:362
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr "Položeno"

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:36
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Passed"
msgstr "Zadovoljava"

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:78 frontend/src/pages/Quizzes.vue:242
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr "Minimalni procenat za prolaz"

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr "Lozinka"

#: frontend/src/pages/CourseForm.vue:206
msgid "Paste the youtube link of a short video introducing the course"
msgstr "Nalepite YouTube link kratkog video-zapisa koji predstavlja obuku"

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr "Plaćanje"

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr "Država plaćanja"

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr "Detalji plaćanja"

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr "Platni portal"

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr "ID plaćanja"

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr "Plaćanje primljeno"

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr "Šablon za podsetnik za plaćanje"

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr "Podešavanje plaćanja"

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr "Plaćanje za "

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr "Plaćanje za sertifikat"

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr "Plaćanje za dokument"

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr "Plaćanje za vrstu dokumenta"

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr "Aplikacija za obradu plaćanja nije instalirana"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:354
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr "Na čekanju"

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr "Procenat"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr "Procenat (npr. 70%)"

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr "Procenat/Status"

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr "Zabeležen korisnički profil"

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr "Broj telefona"

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr "Molimo Vas da dodate <a href='{0}'>{1}</a> za <a href='{2}'>{3}</a> kako biste slali pozivnice za ocenjivanje u kalendar."

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr "Molimo Vas da dodate Zoom nalog da u grupi da biste mogli da kreirate onlajn predavanja."

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr "Molimo Vas da zatražite od administratora da verifikuje Vašu registraciju"

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr "Molimo Vas da proverite svoj imejl za verifikaciju"

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr "Molimo Vas da kliknete na sledeće dugme da postavite novu lozinku"

#: lms/lms/utils.py:2077 lms/lms/utils.py:2081
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr "Molimo Vas da završite prethodne obuke u programu kako biste se upisali na ovu obuku."

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr "Molimo Vas da omogućite Zoom nalog kako biste koristili ovu mogućnost."

#: frontend/src/components/CourseOutline.vue:366
msgid "Please enroll for this course to view this lesson"
msgstr "Molimo Vas da se upišete na ovu obuku da biste pristupili lekciji"

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr "Molimo Vas da se uverite da su sva pitanja završena u roku od {0} minuta."

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr "Molimo Vas da unesete naslov."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr "Molimo Vas da uneste važeći URL."

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr "Molimo Vas da unesete ispravno vreme u formatu HH:mm."

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr "Molimo Vas da unesete važeći vremenski žig"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr "Molimo Vas da unesete URL za podnošenje zadatka."

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr "Molimo Vas da unesete odgovor"

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr "Molimo Vas da instalirate aplikaciju za obradu plaćanja da biste kreirali plaćenu grupu. Za više detalja pogledajte dokumentaciju. {0}"

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr "Molimo Vas da instalirate aplikacije za obradu plaćanja da biste kreirali plaćenu obuku. Za više detalja pogledajte dokumentaciju. {0}"

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr "Molimo Vas da nam kažete kako ste čuli za nas."

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr "Molimo Vas da se prijavite da biste pristupili kvizu."

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr "Molimo Vas da se prijavite da biste pristupili ovoj stranici."

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr "Molimo Vas da se prijavite da biste nastavili sa plaćanjem."

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr "Molimo Vas da se dobro pripremite i stignete na vreme za ocenjivanje."

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:135
msgid "Please run the code to execute the test cases."
msgstr "Molimo Vas da pokrenete kod da biste izvršili test primere."

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr "Molimo Vas da zakažete ocenjivanje kako biste dobili sertifikat."

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr "Molimo Vas da izaberete datum."

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr "Molimo Vas da izaberete trajanje."

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr "Molimo Vas da izaberete budući datum i vreme."

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr "Molimo Vas da izaberete kviz"

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr "Molimo Vas da izaberete vreme."

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr "Molimo Vas da izaberete vremensku zonu."

#: frontend/src/components/Quiz.vue:533
msgid "Please select an option"
msgstr "Molimo Vas da izaberete opciju"

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr "Molimo Vas da preduzmete odgovarajuću radnju na {0}"

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr "Molimo Vas da otpremnice SCORM paket"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr "Molimo Vas da otpremite fajl zadatka."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr "Numerički prikaz ocene (npr. 70)"

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr "Mogućnosti"

#: frontend/src/components/Modals/Question.vue:91
msgid "Possibility"
msgstr "Mogućnost"

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr "Mogući odgovor 1"

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr "Mogući odgovor 2"

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr "Mogući odgovor 3"

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr "Mogući odgovor 4"

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr "Objavi"

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr "Poštanski broj"

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr "Powered by Learning"

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr "Poželjna funkcija"

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr "Poželjne funkcije"

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr "Poželjne industrije"

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr "Poželjna industrija"

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr "Poželjna lokacija"

#. Label of the prevent_skipping_videos (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Prevent Skipping Videos"
msgstr "Spreči preskakanje video-snimaka"

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr "Pregled slike"

#: frontend/src/pages/CourseForm.vue:204
msgid "Preview Video"
msgstr "Pregled video-snimka"

#: frontend/src/pages/Lesson.vue:114
msgid "Previous"
msgstr "Prethodno"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:265
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr "Cene"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:230
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr "Cenovnik i sertifikacija"

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr "Primarne države"

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr "Primarna podgrupa"

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr "Politika privatnosti"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr "Privatno"

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr "Privatne informacije uključuju Vašu ocenu i želje vezane za radno okruženje"

#. Label of the problem_statement (Text Editor) field in DocType 'LMS
#. Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:41
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:25
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Problem Statement"
msgstr "Opis problema"

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr "Nastavi na plaćanje"

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr "Zanimanje"

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr "Profilna slika"

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr "Obuka u programu"

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr "Obuke u programu"

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr "Član programa"

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr "Članovi programa"

#: frontend/src/components/Assessments.vue:249
msgid "Programming Exercise"
msgstr "Vežba programiranja"

#: frontend/src/components/Settings/BadgeForm.vue:200
#: frontend/src/components/Settings/Badges.vue:205
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:420
msgid "Programming Exercise Submission"
msgstr "Podnesak vežbe programiranja"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:411
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:298
msgid "Programming Exercise Submissions"
msgstr "Podnesci vežbe programiranja"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:211
msgid "Programming Exercise created successfully"
msgstr "Vežba programiranja je uspešno kreirana"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:247
msgid "Programming Exercise deleted successfully"
msgstr "Vežba programiranja je uspešno obrisana"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:230
msgid "Programming Exercise updated successfully"
msgstr "Vežba programiranja je uspešno ažurirana"

#. Label of the programming_exercises (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:308
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:158
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:166
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Programming Exercises"
msgstr "Vežbe programiranja"

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr "Programi"

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: frontend/src/components/Modals/CourseProgressSummary.vue:222
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr "Napredak"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr "Napredak (%)"

#: frontend/src/components/Modals/CourseProgressSummary.vue:112
msgid "Progress Distribution"
msgstr "Distribucija napretka"

#: frontend/src/components/CourseCardOverlay.vue:99
msgid "Progress Summary"
msgstr "Rezime napretka"

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr "Napredak studenata na obukama i u procenama"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr "Javni"

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr "Objavi na stranici učesnika"

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:59 frontend/src/pages/CourseForm.vue:159
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr "Objavljeno"

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr "Objavljene obuke"

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:163
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr "Objavljeno na"

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr "Kupljeni sertifikat"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Python"
msgstr "Python"

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:343 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr "Pitanje"

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr "Pitanje "

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr "Detalji pitanja"

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr "Naziv pitanja"

#: frontend/src/components/Modals/Question.vue:284
msgid "Question added successfully"
msgstr "Pitanje je uspešno dodato"

#: frontend/src/components/Modals/Question.vue:334
msgid "Question updated successfully"
msgstr "Pitanje je uspešno ažurirano"

#: frontend/src/components/Quiz.vue:112
msgid "Question {0}"
msgstr "Pitanje {0}"

#: frontend/src/components/Quiz.vue:214
msgid "Question {0} of {1}"
msgstr "Pitanje {0} od {1}"

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:131 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr "Pitanja"

#: frontend/src/pages/QuizForm.vue:385
msgid "Questions deleted successfully"
msgstr "Pitanja su uspešno obrisana"

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Assessments.vue:247
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr "Kviz"

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr "ID kviza"

#. Label of a Link in the LMS Workspace
#: frontend/src/components/Settings/BadgeForm.vue:197
#: frontend/src/components/Settings/Badges.vue:203
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr "Podnošenje kviza"

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr "Podnošenja kviza"

#: frontend/src/components/Quiz.vue:251
msgid "Quiz Summary"
msgstr "Rezime kviza"

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr "Naslov kviza"

#: frontend/src/pages/Quizzes.vue:201
msgid "Quiz created successfully"
msgstr "Kviz je uspešno kreiran"

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr "Kviz nije dostupan gostujućim korisnicima. Molimo Vas da se prijavite da biste nastavili."

#: frontend/src/pages/QuizForm.vue:310
msgid "Quiz updated successfully"
msgstr "Kviz je uspešno ažuriran"

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr "Kviz će biti prikazivan na dnu lekcije."

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/QuizForm.vue:396 frontend/src/pages/Quizzes.vue:275
#: frontend/src/pages/Quizzes.vue:285 lms/www/lms.py:251
msgid "Quizzes"
msgstr "Kvizovi"

#: frontend/src/pages/Quizzes.vue:223
msgid "Quizzes deleted successfully"
msgstr "Kvizovi su uspešno obrisani"

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr "Kvizovi u ovom video-snimku"

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:147
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr "Ocena"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr "Ocena ne može biti 0"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr "Spremno"

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr "Naziv referentnog dokumenta"

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr "DocType referenca"

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr "Vrsta referentnog dokumenta"

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr "Pozdrav"

#: frontend/src/components/BatchOverlay.vue:96
msgid "Register Now"
msgstr "Registrujte se sada"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr "Registrovan"

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr "Registrovano, ali onemogućeno"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr "Odbijeno"

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:215
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr "Srodne obuke"

#: frontend/src/components/Controls/Uploader.vue:34
#: frontend/src/pages/BatchForm.vue:246 frontend/src/pages/CourseForm.vue:136
msgid "Remove"
msgstr "Ukloni"

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr "Odgovori"

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr "Zatraži pozivnicu"

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr "Zahtev za mentorstvo"

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr "Neophodna uloga"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr "Ograničeno"

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr "Rezultat"

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr "CV"

#: frontend/src/components/Quiz.vue:85 frontend/src/components/Quiz.vue:288
msgid "Resume Video"
msgstr "Nastavi video-snimak"

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr "Pregled"

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr "Ocenite ovu obuku"

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr "Pregledao"

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr "Pregledi"

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr "Uloga"

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr "Poželjna uloga"

#: frontend/src/pages/ProfileRoles.vue:117
msgid "Role updated successfully"
msgstr "Uloga je uspešno ažurirana"

#: frontend/src/components/AppSidebar.vue:612
msgid "Roles"
msgstr "Uloge"

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr "Putanja"

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr "Red #{0} sadrži datum van trajanja grupe."

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr "Red #{0} sadrži vreme završetka van trajanja grupe."

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr "Red #{0} sadrži vreme početka koje je veće ili jednako vremenu završetka."

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr "Red #{0} sadrži vreme početka van trajanja grupe."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr "Redovi {0} sadrže duplikate pitanja."

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:56
#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr "Pokreni"

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr "SCORM"

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr "SCORM paket"

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr "SCORM putanja paketa"

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr "SEO"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr "Subota"

#: frontend/src/components/AssessmentPlugin.vue:12
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/Code.vue:24
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/Question.vue:112
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:12
#: frontend/src/components/Settings/BadgeForm.vue:78
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:101
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:9
#: frontend/src/pages/QuizForm.vue:43 frontend/src/pages/QuizSubmission.vue:14
#: frontend/src/pages/Quizzes.vue:105
msgid "Save"
msgstr "Sačuvaj"

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr "Raspored"

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr "Zakaži ocenjivanje"

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr "Planirani tok"

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr "Opseg"

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr "Ocena"

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr "Rezultat od"

#: frontend/src/components/Settings/Evaluators.vue:25
#: frontend/src/components/Settings/Members.vue:25
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr "Pretraga"

#: frontend/src/components/Modals/CourseProgressSummary.vue:18
msgid "Search by Member Name"
msgstr "Pretraži po imenu člana"

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr "Pretraga po nazivu"

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr "Pretraga po naslovu"

#: frontend/src/pages/Assignments.vue:34
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:47
msgid "Search by title"
msgstr "Pretraga po naslovu"

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr "Pretraži ikonicu"

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:154
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr "Broj mesta"

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr "Preostalo mesto"

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr "Broj mesta ne može biti negativan."

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr "Preostala mesta"

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:42
msgid "Select Date"
msgstr "Izaberite datum"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:23
msgid "Select a Programming Exercise"
msgstr "Izaberite vežbu programiranja"

#: frontend/src/components/Modals/Question.vue:101
msgid "Select a question"
msgstr "Izaberite pitanje"

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select a quiz"
msgstr "Izaberite kviz"

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr "Izaberite termin"

#: frontend/src/components/AssessmentPlugin.vue:35
msgid "Select an assignment"
msgstr "Izaberite zadatak"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr "Pošalji imejl potvrde"

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr "Pošalji kalendarsku pozivnicu za ocenjivanje"

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr "Sesije po danima"

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr "Postavite svoju lozinku"

#: frontend/src/components/AppSidebar.vue:560
msgid "Setting up"
msgstr "Podešavanje"

#: frontend/src/components/AppSidebar.vue:605
msgid "Setting up payment gateway"
msgstr "Podešavanje platnog portala"

#: frontend/src/components/AppSidebar.vue:610
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:53 frontend/src/pages/CourseForm.vue:152
#: frontend/src/pages/ProfileRoles.vue:4
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:19
#: frontend/src/pages/QuizForm.vue:86
msgid "Settings"
msgstr "Podešavanja"

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr "Podeli na"

#: frontend/src/pages/BatchForm.vue:42
msgid "Short Description"
msgstr "Kratak opis"

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:86
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr "Kratak uvod"

#: frontend/src/pages/BatchForm.vue:45
msgid "Short description of the batch"
msgstr "Kratak opis grupe"

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr "Prikaži odgovor"

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:93 frontend/src/pages/Quizzes.vue:256
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr "Prikaži odgovore"

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:98 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr "Prikaži istoriju podnesaka"

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr "Prikaži karticu u grupi"

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr "Prikaži ekvivalent američkog dolara (USD)"

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr "Prikaži dnevni prikaz u rasporedu nastave"

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr "Prikaži onlajn predavanja"

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:105 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr "Promešaj pitanja"

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr "Bočna traka"

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr "Stavke bočne trake"

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr "Registracija je onemogućena"

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr "Registruj se"

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr "Imejl za registraciju"

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr "Podešavanje registracije"

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr "Registracije"

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr "Veština"

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr "Detalji veštine"

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr "Naziv veštine"

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr "Veštine"

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr "Preskoči"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr "Vremenski termini se preklapaju za neke rasporede."

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr "Termin je uspešno dodat"

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr "Termin je uspešno obrisan"

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr "Slug"

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr "Rasprodato"

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr "Rešenje"

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#. Label of the source (Data) field in DocType 'LMS Video Watch Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Source"
msgstr "Izvor"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr "Osoblje"

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr "Faza"

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:81
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr "Početak"

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:82
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr "Datum početka"

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr "Datum početka:"

#: frontend/src/components/CourseCardOverlay.vue:76
#: frontend/src/pages/Lesson.vue:45 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr "Započni učenje"

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr "Vreme početka"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr "Vreme početka ne može biti veće od vremena završetka"

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr "Početni URL"

#: frontend/src/components/Quiz.vue:81
msgid "Start the Quiz"
msgstr "Započni kviz"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr "Startup organizacija"

#: frontend/src/pages/Billing.vue:83
msgid "State/Province"
msgstr "Svojstva stanja"

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr "Statistika"

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#. Label of the status (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the status (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/Badges.vue:228
#: frontend/src/components/Settings/ZoomSettings.vue:197
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:280
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Status"
msgstr "Status"

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr "Status/Rezultat"

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr "Student"

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr "Recenzije studenata"

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr "Studenti"

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr "Studenti su uspešno obrisani"

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr "Studenti će biti upisani u plaćenu grupu nakon što izvrše uplatu"

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr "Podgrupa"

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr "Naslov"

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr "Naslov je neophodan"

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr "Podnesak"

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr "Vrsta podnesaka"

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr "Podneto od strane"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:353
msgid "Submission saved!"
msgstr "Podnesak je sačuvan!"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:254
msgid "Submissions deleted successfully"
msgstr "Podnesci su uspešno obrisani"

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Quiz.vue:242 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr "Podnesi"

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr "Podnesi povratnu informaciju"

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr "Podnesi i nastavi"

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr "Podnesite svoj CV da biste nastavili sa prijavom na ovu poziciju. Nakon predaje, Vaš CV će biti prosleđen oglašivaču posla."

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr "Podneto {0}"

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr "Rezime"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr "Nedelja"

#: lms/lms/api.py:1119
msgid "Suspicious pattern found in {0}: {1}"
msgstr "Sumnjiv obrazac pronađen u {0}: {1}"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr "Sistem menadžer"

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr "Oznake"

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr "Tim škole"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr "Timski rad"

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr "Šablon"

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr "Privremeno onemogućeno"

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr "Uslovi korišćenja"

#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise'
#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:29
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:83
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Test Cases"
msgstr "Test primeri"

#: frontend/src/pages/QuizForm.vue:23
msgid "Test Quiz"
msgstr "Test kviz"

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr "Rezultati testa"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:82
msgid "Test this Exercise"
msgstr "Testiraj ovu vežbu"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:92
msgid "Test {0}"
msgstr "Test {0}"

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr "Testovi"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr "Tekst"

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr "Hvala Vam što ste podelili svoje utiske."

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr "Hvala i srdačan pozdrav"

#: lms/lms/utils.py:1975
msgid "The batch is full. Please contact the Administrator."
msgstr "Grupa je popunjena. Molimo Vas da kontaktirate administratora."

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr "Grupa u koju ste se upisali počinje sutra. Molimo Vas da budete spremni i tačni za sesiju."

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr "Obuka {0} je sada dostupna na {1}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr "Osoba koja ocenjuje ovu obuku nije dostupna u periodu od {0} do {1}. Molimo Vas da izaberete datum nakon {1}"

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr "Kviz je vremenski ograničen. Za svako pitanje biće Vam dodeljeno {0} sekundi."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr "Termin je već rezervisan od strane drugog učesnika."

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr "Status Vaše prijave je promenjen."

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr "U ovoj obuci nema poglavlja. Ovde možete kreirati i uređivati poglavlja."

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr "Nema slobodnih mesta u ovoj grupi."

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr "U ovoj grupi nema studenata."

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr "Nema podnesaka za ovaj zadatak."

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr "Trenutno nema {0}. Pratite nas, uskoro stižu nova iskustva učenja!"

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr "Na ovoj sajtu nema {0}."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr "Došlo je do izmene u Vašem podnesku za zadatak {0}"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr "Došlo je do izmene Vašeg podneska. Imali ste rezultat od {0} poena na kvizu {1}"

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr "Ova prilagođavanja će raditi na glavnoj stranici grupe."

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr "Ovaj bedž je dodeljen za {0} na {1}."

#: frontend/src/components/Settings/BadgeAssignments.vue:92
msgid "This badge has not been assigned to any students yet"
msgstr "Ovaj bedž još uvek nije dodeljen nijednom studentu"

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr "Ovaj sertifikat nema rok trajanja"

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr "Ovo predavanje se završilo"

#: frontend/src/components/CourseCardOverlay.vue:126
msgid "This course has:"
msgstr "Ova obuka sadrži:"

#: lms/lms/utils.py:1818
msgid "This course is free."
msgstr "Ova obuka je besplatna."

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr "Ovaj opis će se prikazivati na listama i stranicama koje nemaju meta opis"

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr "Ova slika će se prikazivati na listama i stranicama koje nemaju podrazumevanu sliku"

#: frontend/src/pages/Lesson.vue:30
msgid "This lesson is locked"
msgstr "Lekcija je zaključana"

#: frontend/src/pages/Lesson.vue:35
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr "Ova lekcija nije dostupna za pregled. Molimo Vas da se upišete na obuku da biste joj pristupili."

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr "Ova lekcija nije dostupna za pregled. Molimo Vas da se pridružite obuci da biste joj pristupili."

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr "Kviz se sastoji od {0} pitanja."

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr "Sajt se ažurira. Trenutno nisu moguće izmene. Pun pristup će uskoro biti vraćen."

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr "Ovaj video-snimak sadrži {0} {1}:"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr "Četvrtak"

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:58
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr "Vreme"

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr "Vremenska preferencija"

#: frontend/src/components/VideoBlock.vue:140
msgid "Time for a Quiz"
msgstr "Vreme za kviz"

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr "Vreme u video-snimku"

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr "Vreme u video-snimku (u minutima)"

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr "Vreme u video-snimku prelazi ukupno trajanje video-snimka."

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr "Vreme mora biti u 24 časovnom formatu (HH:mm). Na primer 11:30 ili 22:00"

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr "Raspored nastave"

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr "Legende rasporeda nastave"

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr "Šablon rasporeda nastave"

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:114
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr "Vremenska zona"

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr "Vremenski termini:"

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Programming Exercise'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/components/Settings/BadgeForm.vue:19
#: frontend/src/pages/Assignments.vue:162 frontend/src/pages/BatchForm.vue:27
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:17
#: frontend/src/pages/Programs.vue:101 frontend/src/pages/QuizForm.vue:56
#: frontend/src/pages/Quizzes.vue:115 frontend/src/pages/Quizzes.vue:229
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr "Naslov"

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr "Naslov je neophodan"

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr "Za"

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr "Datum završetka"

#: lms/lms/utils.py:1829
msgid "To join this batch, please contact the Administrator."
msgstr "Za pridruživanje ovoj grupi, molimo Vas da kontaktirate administratora."

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr "Previše korisnika se registrovalo u poslednje vreme, stoga je registracija privremeno onemogućena. Pokušajte ponovo za sat vremena"

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr "Ukupno"

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:73 frontend/src/pages/Quizzes.vue:235
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr "Ukupan broj poena"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr "Ukupno registracija"

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr "Povratne informacije o treningu"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr "Putovanje"

#: frontend/src/components/Quiz.vue:284 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr "Pokušajte ponovo"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr "Utorak"

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr "Twitter"

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:40 frontend/src/pages/Assignments.vue:167
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:53
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr "Vrsta"

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr "Ukucajte '/' za komande ili označite tekst za formatiranje"

#: frontend/src/components/Quiz.vue:646
msgid "Type your answer"
msgstr "Unesite svoj odgovor"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr "UK sistem ocenjivanja  (npr. prvi, 2:2)"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr "URL"

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr "UUID"

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr "Nedostupnost"

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr "Nedostupnost je uspešno ažurirana"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr "Datum početka nedostupnosti ne može biti nakon datuma završetka nedostupnosti"

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr "Pregled u toku"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr "Nije na listi"

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr "Neobjavljeno"

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr "Unsplash"

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr "Unsplash pristupni ključ"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr "Nestrukturirana uloga"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:171
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr "Predstojeće"

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr "Predstojeće grupe"

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr "Predstojeća ocenjivanja"

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr "Ažuriraj"

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr "Ažuriraj lozinku"

#: frontend/src/components/Controls/Uploader.vue:20
#: frontend/src/pages/BatchForm.vue:227 frontend/src/pages/CourseForm.vue:117
msgid "Upload"
msgstr "Otpremi"

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr "Otpremi fajl"

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr "Otpremanje {0}%"

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr "Koristite HTML"

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: frontend/src/components/Settings/BadgeForm.vue:196
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr "Korisnik"

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr "Kategorija korisnika"

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr "Korisničko polje"

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr "Slika korisnika"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr "Korisnički unos"

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr "Veština korisnika"

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr "Korisnik {0} je prijavio oglas za posao {1}"

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr "Korisničko ime"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr "Korisnici"

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr "Odgovor korisnika"

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr "Neophodni su važeći imejl i naziv"

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr "Vrednost"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr "Promena vrednosti"

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr "Link za umetanje video-snimka"

#: frontend/src/pages/Lesson.vue:19
msgid "Video Statistics"
msgstr "Statistika video-snimka"

#: frontend/src/components/Modals/VideoStatistics.vue:6
msgid "Video Statistics for {0}"
msgstr "Statistika video-snimka za {0}"

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr "Prikaz"

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr "Prikaz sertifikata"

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr "Pogledaj sve povratne informacije"

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr "Vidljivost"

#: frontend/src/components/BatchOverlay.vue:73
msgid "Visit Batch"
msgstr "Poseti grupu"

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr "Poseti veb-sajt"

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr "Posetite sledeći link da biste pogledali svoj "

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr "Posetite svoju grupu"

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr "Volontiranje ili praksa"

#. Label of the watch_time (Data) field in DocType 'LMS Video Watch Duration'
#: frontend/src/components/Modals/VideoStatistics.vue:25
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Watch Time"
msgstr "Vreme gledanja"

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr "Drago nam je da Vas obavestimo da ste upisani u našu predstojeću grupu. Čestitamo!"

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr "Imamo ograničen broj mesta, koja će brzo biti popunjena!"

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr "Primetili smo da ste započeli upis za"

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr "Veb-stranica"

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr "Veb-stranica je dodata u bočnu traku"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr "Sreda"

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr "Dobro došli u {0}!"

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr "Šta najbolje opisuje Vašu ulogu?"

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr "Šta znači uključi u pregled?"

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr "Koja je Vaša namena za Frappe Learning?"

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr "Kada se obuka podnese na pregled, biće prikazana ovde."

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr "Gde ste čuli o nama?"

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr "Uz ovu sertifikaciju sada možete pokazati svoja unapređena znanja i podeliti svoje postignuće sa kolegama i na LinkedIn-u. Da biste pristupili sertifikatu, kliknite na link ispod. Uverite se da ste prijavljeni na portal."

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr "Povučeno"

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr "Radno okruženje"

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr "Radno iskustvo"

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr "Detalji radnog iskustva"

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr "Napišite recenziju"

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr "Napišite recenziju"

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr "Ovde upišite Vaš odgovor"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr "Već postoji ocena na {0} u {1} za obuku {2}."

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr "Već imate sertifikat za ovu obuku. Kliknite na karticu ispod da otvorite svoj sertifikat."

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr "Već ste upisani na ovu grupu."

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr "Već ste upisani na ovu obuku."

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr "Niste član ove grupe. Pogledajte naše predstojeće grupe."

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr "Niste mentor obuke {0}"

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr "Niste upisani u ovu obuku. Molimo Vas da se upišete da biste pristupili ovoj lekciji."

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr "Takođe možete kopirati i nalepiti sledeći link u Vašem internet pretraživaču"

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr "Ovaj kviz možete pokušati da uradite samo {0} {1}"

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr "Ovaj kviz možete pokušati da uradite samo {0}."

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr "Možete pronaći njihov CV koji je priložen u ovom imejlu."

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr "Ne možete menjati dostupnost dok se sajt ažurira."

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr "Ne možete menjati uloge u režimu samo za čitanje."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr "Ne možete zakazati ocenjivanje nakon {0}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr "Ocenjivanje nije moguće zakazati za termine koji su već prošli."

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr "Nemate dozvolu za pristup ovoj stranici."

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr "Nemate nijedno obaveštenje."

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr "Ostvarili ste"

#: frontend/src/components/Quiz.vue:265
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr "Imate {0}% tačnih odgovora sa rezultatom od {1} od mogućih {2}"

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr "Imate onlajn predavanje zakazano za sutra. Molimo Vas da se pripremite i pridružite na vreme."

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr "Već ste se prijavili za ovaj posao."

#: frontend/src/components/Quiz.vue:96 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr "Već ste premašili maksimalan dozvoljeni broj pokušaja za ovaj kviz."

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr "Već ste kupili sertifikat za ovu obuku."

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr "Već ste ocenili ovu obuku"

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr "Prijavili ste se"

#: frontend/src/components/BatchOverlay.vue:181
msgid "You have been enrolled in this batch"
msgstr "Upisani ste u ovu grupu"

#: frontend/src/components/CourseCardOverlay.vue:229
msgid "You have been enrolled in this course"
msgstr "Upisani ste na ovu obuku"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr "Premašili ste maksimalan dozvoljeni broj pokušaja ({0}) za ovaj kviz"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr "Dobili ste rezultat od {0} na kvizu {1}"

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr "Izabrali ste da budete obavešteni o ovoj obuci. Dobićete imejl kada obuka postane dostupna."

#: frontend/src/components/CourseCardOverlay.vue:217
msgid "You need to login first to enroll for this course"
msgstr "Neophodno je da se prvo prijavite da biste se upisali na ovu obuku"

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr "Neophodno je da završite kviz kako biste nastavili video-snimak"

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr "Da biste prošli kviz, morate imati najmanje {0}% tačnih odgovora."

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr "Prijavili ste se da budete mentor za ovu obuku. Vaš zahtev je trenutno na razmatranju."

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr "Uspešno ste podneli zadatak."

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr "YouTube Video URL"

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr "YouTube video-snimak će se prikazati na vrhu lekcije."

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr "Vaš nalog je uspešno kreiran!"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:119
msgid "Your Output"
msgstr "Tvoj izlaz"

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr "Vaša grupa {0} počinje sutra"

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr "Vaš kalendar je podešen."

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr "Vaše onlajn predavanje {0} je danas"

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr "Vaš upis u {{ batch_name }} je potvrđen"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr "Vaše ocenjivanje za obuku {0} je zakazano za {1} u {2} {3}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr "Vaš termin za ocenjivanje je rezervisan"

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr "Osoba koja će Vas ocenjivati je {0}"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr "Vaš zahtev za pridruživanjem kao mentor za obuku"

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr "Vaš rezultat je"

#: frontend/src/components/Quiz.vue:258
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr "Vaš podnesak je uspešno sačuvan. Predavač će je uskoro pregledati i oceniti, a Vi ćete biti obavešteni o konačnom rezultatu."

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr "Zen režim"

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:171
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr "Zoom nalog"

#: frontend/src/components/Modals/ZoomAccountModal.vue:164
msgid "Zoom Account created successfully"
msgstr "Zoom nalog je uspešno kreiran"

#: frontend/src/components/Modals/ZoomAccountModal.vue:202
msgid "Zoom Account updated successfully"
msgstr "Zoom nalog je uspešno ažuriran"

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr "Zoom podešavanje"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr "aktivnosti"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr "aktivnost"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr "kandidat"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr "kandidati"

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr "na {0} minuta"

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr "ali niste završili plaćanje"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr "otkažite svoju prijavu"

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr "sertifikat"

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr "sertifikati"

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr "sertifikovani članovi"

#: frontend/src/pages/Lesson.vue:98 frontend/src/pages/Lesson.vue:234
msgid "completed"
msgstr "završeno"

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr "tačnih odgovora"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr "je"

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr "u poslednjih"

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr "<EMAIL>"

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr "član"

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr "članovi"

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr "minute"

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr "od"

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr "od ukupno"

#: frontend/src/pages/QuizForm.vue:344
msgid "question_detail"
msgstr "question_detail"

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr "ocene"

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr "čuvanje..."

#: lms/templates/reviews.html:43
msgid "stars"
msgstr "zvezde"

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr "da biste pogledali svoje povratne informacije."

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr "nedelje"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr "možete"

#: frontend/src/pages/Assignments.vue:26
msgid "{0} Assignments"
msgstr "{0} zadataka"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:39
msgid "{0} Exercises"
msgstr "{0} vežbi"

#: frontend/src/components/Modals/CourseProgressSummary.vue:14
msgid "{0} Members"
msgstr "{0} članova"

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr "{0} otvorenih poslova"

#: frontend/src/pages/Quizzes.vue:18
msgid "{0} Quizzes"
msgstr "{0} kvizova"

#: lms/lms/api.py:886 lms/lms/api.py:894
msgid "{0} Settings not found"
msgstr "{0} podešavanja nisu pronađena"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:12
msgid "{0} Submissions"
msgstr "{0} podnesaka"

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr "Prijava za radno mesto {1} je podneta od strane {0}"

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr "{0} je prijavio oglas za posao iz sledećeg razloga."

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr "Zadatak {1} je podnet od strane {0}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr "{0} je već student obuke {1} u okviru grupe {2}"

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr "{0} je već mentor za obuku {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr "{0} je već {1} za obuku {2}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr "Sertifikat za grupu {1} je već dodeljen korisniku {0}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr "{0} već poseduje sertifikat za obuku {1}"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr "{0} je Vaša osoba za ocenjivanje"

#: lms/lms/utils.py:686
msgid "{0} mentioned you in a comment"
msgstr "pomenuti ste u komentaru od strane {0}"

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr "pomenuti ste u komentaru u svojoj grupi od strane {0}"

#: lms/lms/utils.py:639 lms/lms/utils.py:645
msgid "{0} mentioned you in a comment in {1}"
msgstr "pomenuti ste u komentaru u okviru {1} od strane {0}"

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr "{0}k"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr "{} aktivnih"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr "{} završeno"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr "{} upisan"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr "{} odobreno"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr "{} položeno"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr "{} objavljeno"

