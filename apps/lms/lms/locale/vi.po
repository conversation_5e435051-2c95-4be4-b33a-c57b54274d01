msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-07-11 16:04+0000\n"
"PO-Revision-Date: 2025-07-14 20:03\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Vietnamese\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: vi\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: vi_VN\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr ""

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr ""

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr ""

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr ""

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr ""

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr ""

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr ""

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr ""

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr ""

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr ""

#: frontend/src/pages/CourseForm.vue:88
msgid "A one line introduction to the course that appears on the course card"
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr ""

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr ""

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr ""

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr ""

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: frontend/src/components/Settings/ZoomSettings.vue:192
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr ""

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr ""

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:93
#: frontend/src/components/Settings/Members.vue:91
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr ""

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:91
msgid "Add Evaluator"
msgstr ""

#: frontend/src/components/CourseOutline.vue:146
msgid "Add Lesson"
msgstr ""

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr ""

#: frontend/src/components/Controls/ChildTable.vue:69
msgid "Add Row"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:35
msgid "Add Test Case"
msgstr ""

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr ""

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr ""

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr ""

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a chapter"
msgstr ""

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr ""

#: frontend/src/pages/CourseForm.vue:69
msgid "Add a keyword and then press enter"
msgstr ""

#: frontend/src/components/AppSidebar.vue:569
msgid "Add a lesson"
msgstr ""

#: frontend/src/components/Settings/Members.vue:88
msgid "Add a new member"
msgstr ""

#: frontend/src/components/Modals/Question.vue:166
#: frontend/src/pages/QuizForm.vue:200
msgid "Add a new question"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:5
msgid "Add a programming exercise to your lesson"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:7
msgid "Add a quiz to your lesson"
msgstr ""

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:8
msgid "Add an assignment to your lesson"
msgstr ""

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr ""

#: frontend/src/components/AppSidebar.vue:532
msgid "Add courses to your batch"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr ""

#: frontend/src/components/AppSidebar.vue:511
msgid "Add students to your batch"
msgstr ""

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr ""

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr ""

#: frontend/src/components/AppSidebar.vue:444
msgid "Add your first chapter"
msgstr ""

#: frontend/src/components/AppSidebar.vue:460
msgid "Add your first lesson"
msgstr ""

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr ""

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr ""

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr ""

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr ""

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr ""

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr ""

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr ""

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr ""

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr ""

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr ""

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr ""

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr ""

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr ""

#: frontend/src/pages/BatchForm.vue:64
msgid "Allow self enrollment"
msgstr ""

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr ""

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:275 frontend/src/pages/CourseForm.vue:254
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr ""

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr ""

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr ""

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr ""

#: frontend/src/pages/CourseForm.vue:121 frontend/src/pages/CourseForm.vue:140
msgid "Appears on the course card in the course list"
msgstr ""

#: frontend/src/pages/BatchForm.vue:250
msgid "Appears when the batch URL is shared on any online platform"
msgstr ""

#: frontend/src/pages/BatchForm.vue:231
msgid "Appears when the batch URL is shared on socials"
msgstr ""

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr ""

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr ""

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr ""

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr ""

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr ""

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr ""

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr ""

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr ""

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr ""

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr ""

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr ""

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:581
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr ""

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:28
msgid "Assign For"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:58
msgid "Assign To"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:7
msgid "Assign a Badge"
msgstr ""

#: frontend/src/components/Settings/Badges.vue:221
msgid "Assigned For"
msgstr ""

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/components/Assessments.vue:245
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr ""

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:198
#: frontend/src/components/Settings/Badges.vue:204
msgid "Assignment Submission"
msgstr ""

#: frontend/src/pages/AssignmentSubmissionList.vue:222
msgid "Assignment Submissions"
msgstr ""

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr ""

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr ""

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr ""

#: frontend/src/components/AppSidebar.vue:585
#: frontend/src/components/Settings/Badges.vue:163
#: frontend/src/pages/Assignments.vue:208 lms/www/lms.py:273
msgid "Assignments"
msgstr ""

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr ""

#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.py:15
msgid "At least one test case is required for the programming exercise."
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr ""

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr ""

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr ""

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr ""

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:104
msgid "Average Progress %"
msgstr ""

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr ""

#: frontend/src/components/Modals/VideoStatistics.vue:65
msgid "Average Watch Time (seconds)"
msgstr ""

#: frontend/src/pages/Lesson.vue:151
msgid "Back to Course"
msgstr ""

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:32
#: frontend/src/components/Settings/Badges.vue:214
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr ""

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr ""

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:131
msgid "Badge assignment created successfully"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:112
msgid "Badge assignment updated successfully"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignments.vue:173
msgid "Badge assignments deleted successfully"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:182
msgid "Badge created successfully"
msgstr ""

#: frontend/src/components/Settings/Badges.vue:190
msgid "Badge deleted successfully"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:162
msgid "Badge updated successfully"
msgstr ""

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: frontend/src/components/Settings/BadgeForm.vue:195
#: frontend/src/components/Settings/Badges.vue:200
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr ""

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr ""

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr ""

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:133
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr ""

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:204
#: frontend/src/components/Settings/Badges.vue:202
msgid "Batch Enrollment"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr ""

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr ""

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr ""

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr ""

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr ""

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr ""

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr ""

#: frontend/src/pages/BatchForm.vue:578
msgid "Batch deleted successfully"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr ""

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr ""

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr ""

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr ""

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr ""

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr ""

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr ""

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr ""

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr ""

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr ""

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr ""

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:54
msgid "Buy this course"
msgstr ""

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr ""

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr ""

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr ""

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:199 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr ""

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr ""

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr ""

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:156
msgid "Certificate of Completion"
msgstr ""

#: frontend/src/components/Modals/Event.vue:317
msgid "Certificate saved successfully"
msgstr ""

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr ""

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr ""

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:589
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:381
#: frontend/src/pages/BatchForm.vue:69 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr ""

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr ""

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr ""

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr ""

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr ""

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr ""

#: lms/templates/assignment.html:13
msgid "Change"
msgstr ""

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr ""

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of the chapter (Link) field in DocType 'LMS Video Watch Duration'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr ""

#: frontend/src/components/CourseOutline.vue:337
msgid "Chapter deleted successfully"
msgstr ""

#: frontend/src/components/CourseOutline.vue:271
msgid "Chapter moved successfully"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr ""

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr ""

#: frontend/src/components/Quiz.vue:229 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:16
msgid "Check All Submissions"
msgstr ""

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:97
msgid "Check Submission"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:55
#: frontend/src/pages/QuizForm.vue:39
msgid "Check Submissions"
msgstr ""

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr ""

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr ""

#: frontend/src/components/Quiz.vue:644 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr ""

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr ""

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr ""

#: frontend/src/components/Quiz.vue:645 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr ""

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr ""

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr ""

#: frontend/src/components/Controls/Link.vue:50
msgid "Clear"
msgstr ""

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr ""

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr ""

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:27
msgid "Close"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr ""

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr ""

#. Label of the code (Code) field in DocType 'LMS Exercise'
#. Label of the code (Code) field in DocType 'LMS Programming Exercise
#. Submission'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Code"
msgstr ""

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr ""

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr ""

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr ""

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr ""

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr ""

#: frontend/src/pages/BatchForm.vue:303 frontend/src/pages/CourseForm.vue:292
msgid "Comma separated keywords for SEO"
msgstr ""

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr ""

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr ""

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr ""

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr ""

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr ""

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr ""

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr ""

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr ""

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr ""

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:69
msgid "Compiler Message"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr ""

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr ""

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr ""

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr ""

#: frontend/src/components/VideoBlock.vue:144
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr ""

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:241
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr ""

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/BadgeForm.vue:65
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr ""

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr ""

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr ""

#: frontend/src/pages/BatchForm.vue:148
msgid "Configurations"
msgstr ""

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr ""

#: frontend/src/pages/BatchForm.vue:556
msgid "Confirm your action to delete"
msgstr ""

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr ""

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr ""

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:63
#: frontend/src/pages/Lesson.vue:53
msgid "Contact the Administrator to enroll for this course."
msgstr ""

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:33
msgid "Continue Learning"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr ""

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr ""

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:189
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr ""

#: frontend/src/components/Modals/Question.vue:79
msgid "Correct Answer"
msgstr ""

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr ""

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: frontend/src/components/Settings/BadgeForm.vue:194
#: frontend/src/components/Settings/Badges.vue:199
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr ""

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr ""

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr ""

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr ""

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Course Creator"
msgstr ""

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr ""

#: frontend/src/pages/CourseForm.vue:190
msgid "Course Description"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:203
#: frontend/src/components/Settings/Badges.vue:201
msgid "Course Enrollment"
msgstr ""

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr ""

#: frontend/src/pages/CourseForm.vue:96
msgid "Course Image"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr ""

#: lms/www/lms.py:87
msgid "Course List"
msgstr ""

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr ""

#: frontend/src/pages/CourseDetail.vue:78 frontend/src/pages/CourseForm.vue:302
msgid "Course Outline"
msgstr ""

#. Name of a report
#: frontend/src/components/Modals/CourseProgressSummary.vue:5
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr ""

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr ""

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr ""

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr ""

#: frontend/src/pages/CourseForm.vue:537
msgid "Course created successfully"
msgstr ""

#: frontend/src/pages/CourseForm.vue:574
msgid "Course deleted successfully"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr ""

#: frontend/src/pages/CourseForm.vue:557
msgid "Course updated successfully"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr ""

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr ""

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr ""

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr ""

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:32
#: frontend/src/pages/Programs.vue:93 frontend/src/pages/Quizzes.vue:10
msgid "Create"
msgstr ""

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr ""

#: frontend/src/components/Controls/Link.vue:38
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:7
msgid "Create Programming Exercise"
msgstr ""

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr ""

#: frontend/src/pages/Quizzes.vue:101
msgid "Create a Quiz"
msgstr ""

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a batch"
msgstr ""

#: frontend/src/components/AppSidebar.vue:567
msgid "Create a course"
msgstr ""

#: frontend/src/components/AppSidebar.vue:577
msgid "Create a live class"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:5
msgid "Create a new Badge"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr ""

#: frontend/src/components/AppSidebar.vue:501
msgid "Create your first batch"
msgstr ""

#: frontend/src/components/AppSidebar.vue:432
msgid "Create your first course"
msgstr ""

#: frontend/src/components/AppSidebar.vue:479
msgid "Create your first quiz"
msgstr ""

#: frontend/src/pages/Assignments.vue:173 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr ""

#: frontend/src/components/AppSidebar.vue:573
msgid "Creating a batch"
msgstr ""

#: frontend/src/components/AppSidebar.vue:564
msgid "Creating a course"
msgstr ""

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:282 frontend/src/pages/CourseForm.vue:271
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr ""

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr ""

#: frontend/src/components/AppSidebar.vue:595
msgid "Custom Certificate Templates"
msgstr ""

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr ""

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr ""

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr ""

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr ""

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr ""

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr ""

#: frontend/src/pages/BatchForm.vue:76
msgid "Date and Time"
msgstr ""

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr ""

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr ""

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr ""

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr ""

#: frontend/src/components/Controls/ChildTable.vue:56
#: frontend/src/components/CourseOutline.vue:283
#: frontend/src/components/CourseOutline.vue:349
#: frontend/src/components/Settings/Badges.vue:171
#: frontend/src/pages/BatchForm.vue:562 frontend/src/pages/CourseForm.vue:587
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:67
msgid "Delete"
msgstr ""

#: frontend/src/components/CourseOutline.vue:67
msgid "Delete Chapter"
msgstr ""

#: frontend/src/pages/CourseForm.vue:581
msgid "Delete Course"
msgstr ""

#: frontend/src/components/CourseOutline.vue:343
msgid "Delete this chapter?"
msgstr ""

#: frontend/src/components/CourseOutline.vue:277
msgid "Delete this lesson?"
msgstr ""

#: frontend/src/pages/CourseForm.vue:582
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr ""

#: frontend/src/pages/BatchForm.vue:557
msgid "Deleting this batch will also delete all its data including enrolled students, linked courses, assessments, feedback and discussions. Are you sure you want to continue?"
msgstr ""

#: frontend/src/components/CourseOutline.vue:344
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr ""

#: frontend/src/components/CourseOutline.vue:278
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr ""

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/components/Settings/BadgeForm.vue:32
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr ""

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr ""

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:21 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:50
msgid "Details"
msgstr ""

#: frontend/src/pages/CourseForm.vue:181
msgid "Disable Self Enrollment"
msgstr ""

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr ""

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr ""

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/Badges.vue:56
#: frontend/src/components/Settings/ZoomSettings.vue:66
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr ""

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr ""

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr ""

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr ""

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr ""

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr ""

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr ""

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr ""

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr ""

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr ""

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:129
#: frontend/src/components/CourseCardOverlay.vue:116
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/components/Settings/Badges.vue:156
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:130
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:5
msgid "Edit Badge"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:8
msgid "Edit Badge Assignment"
msgstr ""

#: frontend/src/components/CourseOutline.vue:60
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr ""

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:8
msgid "Edit Programming Exercise"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr ""

#: frontend/src/pages/QuizForm.vue:199
msgid "Edit the question"
msgstr ""

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr ""

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:105
#: frontend/src/components/Settings/Members.vue:103
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr ""

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr ""

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr ""

#: frontend/src/pages/BatchForm.vue:161
msgid "Email Template"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr ""

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr ""

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Email Templates deleted successfully"
msgstr ""

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr ""

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr ""

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr ""

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr ""

#. Label of the enable_negative_marking (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Enable Negative Marking"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr ""

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/BadgeForm.vue:14
#: frontend/src/components/Settings/Badges.vue:53
#: frontend/src/components/Settings/ZoomSettings.vue:63
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr ""

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr ""

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:89 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr ""

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr ""

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:105
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr ""

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:113
msgid "Enroll Now"
msgstr ""

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr ""

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:138
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr ""

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr ""

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr ""

#: lms/lms/utils.py:1943
msgid "Enrollment Failed"
msgstr ""

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/CourseProgressSummary.vue:97
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr ""

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr ""

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr ""

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:169
msgid "Error creating Zoom Account"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:186
msgid "Error creating badge"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr ""

#: frontend/src/pages/Quizzes.vue:212
msgid "Error creating quiz: {0}"
msgstr ""

#: frontend/src/components/Settings/Badges.vue:193
msgid "Error deleting badge"
msgstr ""

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:179
msgid "Error deleting email templates"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:207
msgid "Error updating Zoom Account"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr ""

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:374 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr ""

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr ""

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:122
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr ""

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr ""

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr ""

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:260 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr ""

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:163
msgid "Evaluator added successfully"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:196
msgid "Evaluator deleted successfully"
msgstr ""

#: lms/lms/api.py:1463
msgid "Evaluator does not exist."
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr ""

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Settings/BadgeForm.vue:51
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr ""

#: frontend/src/pages/BatchForm.vue:116
msgid "Example: IST (+5:30)"
msgstr ""

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#. Label of the exercise (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:274
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr ""

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#. Label of the exercise_title (Data) field in DocType 'LMS Programming
#. Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise Title"
msgstr ""

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr ""

#. Label of the expected_output (Data) field in DocType 'LMS Test Case'
#. Label of the expected_output (Data) field in DocType 'LMS Test Case
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:127
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Expected Output"
msgstr ""

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr ""

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr ""

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:75
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr ""

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr ""

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:366
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:37
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Failed"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:136
msgid "Failed to create badge assignment: "
msgstr ""

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:358
msgid "Failed to submit. Please try again. {0}"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:117
msgid "Failed to update badge assignment: "
msgstr ""

#: frontend/src/utils/index.js:671
msgid "Failed to update meta tags {0}"
msgstr ""

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:176
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr ""

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr ""

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr ""

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr ""

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr ""

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:23
msgid "Filter by Exercise"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:28
msgid "Filter by Member"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:39
msgid "Filter by Status"
msgstr ""

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:110
msgid "First Name"
msgstr ""

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr ""

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr ""

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr ""

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr ""

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr ""

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr ""

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr ""

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr ""

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr ""

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr ""

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr ""

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr ""

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:89
msgid "Get Certificate"
msgstr ""

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr ""

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr ""

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr ""

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr ""

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr ""

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr ""

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr ""

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:46
#: frontend/src/components/Settings/Badges.vue:235
msgid "Grant Only Once"
msgstr ""

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr ""

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr ""

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr ""

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr ""

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr ""

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr ""

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr ""

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr ""

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr ""

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr ""

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr ""

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr ""

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr ""

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr ""

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr ""

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr ""

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr ""

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr ""

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr ""

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr ""

#: frontend/src/pages/QuizForm.vue:338
msgid "ID"
msgstr ""

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr ""

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr ""

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr ""

#: frontend/src/components/Quiz.vue:46
msgid "If you answer incorrectly, {0} {1} will be deducted from your score for each incorrect answer."
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr ""

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr ""

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr ""

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr ""

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr ""

#: lms/lms/doctype/lms_quiz/lms_quiz.py:66
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr ""

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr ""

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr ""

#: lms/lms/doctype/lms_quiz/lms_quiz.py:232
msgid "Image: Corrupted Data Stream"
msgstr ""

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:358
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr ""

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:194
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr ""

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr ""

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr ""

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr ""

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr ""

#. Label of the input (Data) field in DocType 'LMS Test Case'
#. Label of the input (Data) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:113
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Input"
msgstr ""

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr ""

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr ""

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr ""

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:184 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr ""

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:34 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr ""

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr ""

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr ""

#: frontend/src/components/AppSidebar.vue:556
#: frontend/src/components/AppSidebar.vue:559
msgid "Introduction"
msgstr ""

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr ""

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr ""

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr ""

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr ""

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr ""

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr ""

#: frontend/src/components/AppSidebar.vue:490
msgid "Invite your team and students"
msgstr ""

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr ""

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr ""

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr ""

#: frontend/src/components/AppSidebar.vue:592
msgid "Issue a Certificate"
msgstr ""

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:37
#: frontend/src/components/Settings/BadgeAssignments.vue:185
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr ""

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr ""

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr ""

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "JavaScript"
msgstr ""

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr ""

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr ""

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr ""

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr ""

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr ""

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr ""

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr ""

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr ""

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr ""

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr ""

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr ""

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr ""

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr ""

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr ""

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr ""

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr ""

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "LMS Programming Exercise"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "LMS Programming Exercise Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr ""

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_test_case/lms_test_case.json
msgid "LMS Test Case"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "LMS Test Case Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "LMS Video Watch Duration"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr ""

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr ""

#. Label of the language (Select) field in DocType 'LMS Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:22
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Language"
msgstr ""

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr ""

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr ""

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr ""

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr ""

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr ""

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr ""

#: frontend/src/pages/LessonForm.vue:426
msgid "Lesson created successfully"
msgstr ""

#: frontend/src/components/CourseOutline.vue:242
msgid "Lesson deleted successfully"
msgstr ""

#: frontend/src/components/CourseOutline.vue:257
msgid "Lesson moved successfully"
msgstr ""

#: frontend/src/pages/LessonForm.vue:450
msgid "Lesson updated successfully"
msgstr ""

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:131
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr ""

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr ""

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr ""

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:110 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr ""

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr ""

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr ""

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr ""

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr ""

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:87
#: frontend/src/components/Settings/Evaluators.vue:81
#: frontend/src/components/Settings/Members.vue:79
#: frontend/src/pages/Assignments.vue:66 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:129
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:87
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:94
msgid "Load More"
msgstr ""

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr ""

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr ""

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr ""

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:59
msgid "Login"
msgstr ""

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr ""

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr ""

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr ""

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr ""

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr ""

#: frontend/src/components/BatchOverlay.vue:73
msgid "Manage Batch"
msgstr ""

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr ""

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr ""

#: frontend/src/components/Quiz.vue:120
msgid "Mark"
msgstr ""

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr ""

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:106
#: frontend/src/components/Quiz.vue:120 frontend/src/pages/QuizForm.vue:348
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr ""

#. Label of the marks_to_cut (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Marks To Cut"
msgstr ""

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr ""

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr ""

#: frontend/src/pages/QuizForm.vue:122
msgid "Marks to Cut"
msgstr ""

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/Quizzes.vue:249 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr ""

#: frontend/src/pages/QuizForm.vue:62
msgid "Maximum Attempts"
msgstr ""

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:194
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr ""

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr ""

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr ""

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/CourseProgressSummary.vue:216
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/VideoStatistics.vue:22
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:26
#: frontend/src/components/Settings/BadgeAssignments.vue:179
#: frontend/src/components/Settings/BadgeForm.vue:215
#: frontend/src/components/Settings/ZoomSettings.vue:187
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:268
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr ""

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr ""

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr ""

#. Label of the member_image (Attach Image) field in DocType 'LMS Badge
#. Assignment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Enrollment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_image (Attach) field in DocType 'LMS Programming
#. Exercise Submission'
#. Label of the member_image (Attach Image) field in DocType 'LMS Video Watch
#. Duration'
#. Label of the member_image (Attach Image) field in DocType 'LMS Zoom
#. Settings'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Member Image"
msgstr ""

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Video Watch Duration'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr ""

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr ""

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr ""

#. Label of the member_username (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_username (Data) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Username"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr ""

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr ""

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr ""

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr ""

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr ""

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr ""

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr ""

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr ""

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr ""

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr ""

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:294 frontend/src/pages/CourseForm.vue:283
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr ""

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:207
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr ""

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:300 frontend/src/pages/CourseForm.vue:289
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr ""

#: frontend/src/pages/BatchForm.vue:289 frontend/src/pages/CourseForm.vue:278
msgid "Meta Tags"
msgstr ""

#: lms/lms/api.py:1503
msgid "Meta tags should be a list."
msgstr ""

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr ""

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr ""

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:286
#: frontend/src/pages/Quizzes.vue:263
msgid "Modified"
msgstr ""

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr ""

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr ""

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr ""

#: frontend/src/components/AppSidebar.vue:600
msgid "Monetization"
msgstr ""

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr ""

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr ""

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/BadgeAssignments.vue:21
#: frontend/src/components/Settings/Badges.vue:21
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:17
#: frontend/src/components/Settings/Members.vue:17
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Courses.vue:310 frontend/src/pages/Programs.vue:14
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr ""

#: lms/www/lms.py:151
msgid "New Batch"
msgstr ""

#: frontend/src/pages/CourseForm.vue:668 lms/www/lms.py:95
msgid "New Course"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr ""

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr ""

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr ""

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr ""

#: frontend/src/pages/QuizForm.vue:137
msgid "New Question"
msgstr ""

#: frontend/src/pages/QuizForm.vue:404 frontend/src/pages/QuizForm.vue:412
msgid "New Quiz"
msgstr ""

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr ""

#: lms/lms/utils.py:609
msgid "New comment in batch {0}"
msgstr ""

#: lms/lms/utils.py:602
msgid "New reply on the topic {0} in course {1}"
msgstr ""

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr ""

#: frontend/src/components/Quiz.vue:237 frontend/src/pages/Lesson.vue:139
msgid "Next"
msgstr ""

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr ""

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignments.vue:87
msgid "No Assignments"
msgstr ""

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr ""

#: frontend/src/components/Quiz.vue:307
msgid "No Quiz submissions found"
msgstr ""

#: frontend/src/pages/Quizzes.vue:19
msgid "No Quizzes"
msgstr ""

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:13
msgid "No Submissions"
msgstr ""

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr ""

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr ""

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr ""

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr ""

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr ""

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr ""

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr ""

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr ""

#: frontend/src/pages/QuizForm.vue:188
msgid "No questions added yet"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr ""

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr ""

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr ""

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr ""

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr ""

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr ""

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr ""

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr ""

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr ""

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizForm.vue:8 frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr ""

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr ""

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr ""

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr ""

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr ""

#: frontend/src/pages/BatchForm.vue:157
msgid "Number of seats available"
msgstr ""

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr ""

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr ""

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr ""

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr ""

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr ""

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr ""

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr ""

#: frontend/src/utils/index.js:502
msgid "Only image file is allowed."
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr ""

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr ""

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr ""

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:70
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr ""

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr ""

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr ""

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr ""

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr ""

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr ""

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr ""

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr ""

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr ""

#. Label of the output (Data) field in DocType 'LMS Test Case Submission'
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Output"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:216
#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr ""

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr ""

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr ""

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr ""

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:270
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr ""

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:246
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:165
msgid "Paid Certificate after Evaluation"
msgstr ""

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:236
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr ""

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:362
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:36
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Passed"
msgstr ""

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:78 frontend/src/pages/Quizzes.vue:242
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr ""

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr ""

#: frontend/src/pages/CourseForm.vue:206
msgid "Paste the youtube link of a short video introducing the course"
msgstr ""

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr ""

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr ""

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr ""

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr ""

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr ""

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr ""

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr ""

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr ""

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr ""

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr ""

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr ""

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:354
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr ""

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr ""

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr ""

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr ""

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr ""

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr ""

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr ""

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr ""

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr ""

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr ""

#: lms/lms/utils.py:2077 lms/lms/utils.py:2081
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr ""

#: frontend/src/components/CourseOutline.vue:366
msgid "Please enroll for this course to view this lesson"
msgstr ""

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr ""

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr ""

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr ""

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr ""

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr ""

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr ""

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:135
msgid "Please run the code to execute the test cases."
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr ""

#: frontend/src/components/Quiz.vue:533
msgid "Please select an option"
msgstr ""

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr ""

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr ""

#: frontend/src/components/Modals/Question.vue:91
msgid "Possibility"
msgstr ""

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr ""

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr ""

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr ""

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr ""

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr ""

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr ""

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr ""

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr ""

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr ""

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr ""

#. Label of the prevent_skipping_videos (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Prevent Skipping Videos"
msgstr ""

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr ""

#: frontend/src/pages/CourseForm.vue:204
msgid "Preview Video"
msgstr ""

#: frontend/src/pages/Lesson.vue:114
msgid "Previous"
msgstr ""

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:265
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr ""

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:230
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr ""

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr ""

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr ""

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr ""

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr ""

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr ""

#. Label of the problem_statement (Text Editor) field in DocType 'LMS
#. Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:41
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:25
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Problem Statement"
msgstr ""

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr ""

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr ""

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr ""

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr ""

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr ""

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr ""

#: frontend/src/components/Assessments.vue:249
msgid "Programming Exercise"
msgstr ""

#: frontend/src/components/Settings/BadgeForm.vue:200
#: frontend/src/components/Settings/Badges.vue:205
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:420
msgid "Programming Exercise Submission"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:411
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:298
msgid "Programming Exercise Submissions"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:211
msgid "Programming Exercise created successfully"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:247
msgid "Programming Exercise deleted successfully"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:230
msgid "Programming Exercise updated successfully"
msgstr ""

#. Label of the programming_exercises (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:308
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:158
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:166
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Programming Exercises"
msgstr ""

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr ""

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: frontend/src/components/Modals/CourseProgressSummary.vue:222
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr ""

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:112
msgid "Progress Distribution"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:99
msgid "Progress Summary"
msgstr ""

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr ""

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr ""

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr ""

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:59 frontend/src/pages/CourseForm.vue:159
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr ""

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr ""

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:163
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr ""

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr ""

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Python"
msgstr ""

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:343 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr ""

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr ""

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr ""

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr ""

#: frontend/src/components/Modals/Question.vue:284
msgid "Question added successfully"
msgstr ""

#: frontend/src/components/Modals/Question.vue:334
msgid "Question updated successfully"
msgstr ""

#: frontend/src/components/Quiz.vue:112
msgid "Question {0}"
msgstr ""

#: frontend/src/components/Quiz.vue:214
msgid "Question {0} of {1}"
msgstr ""

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:131 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr ""

#: frontend/src/pages/QuizForm.vue:385
msgid "Questions deleted successfully"
msgstr ""

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Assessments.vue:247
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr ""

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr ""

#. Label of a Link in the LMS Workspace
#: frontend/src/components/Settings/BadgeForm.vue:197
#: frontend/src/components/Settings/Badges.vue:203
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr ""

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr ""

#: frontend/src/components/Quiz.vue:251
msgid "Quiz Summary"
msgstr ""

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr ""

#: frontend/src/pages/Quizzes.vue:201
msgid "Quiz created successfully"
msgstr ""

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr ""

#: frontend/src/pages/QuizForm.vue:310
msgid "Quiz updated successfully"
msgstr ""

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr ""

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/QuizForm.vue:396 frontend/src/pages/Quizzes.vue:275
#: frontend/src/pages/Quizzes.vue:285 lms/www/lms.py:251
msgid "Quizzes"
msgstr ""

#: frontend/src/pages/Quizzes.vue:223
msgid "Quizzes deleted successfully"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr ""

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:147
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr ""

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr ""

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr ""

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr ""

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr ""

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr ""

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:96
msgid "Register Now"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr ""

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr ""

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:215
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr ""

#: frontend/src/components/Controls/Uploader.vue:34
#: frontend/src/pages/BatchForm.vue:246 frontend/src/pages/CourseForm.vue:136
msgid "Remove"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr ""

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr ""

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr ""

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr ""

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr ""

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr ""

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr ""

#: frontend/src/components/Quiz.vue:85 frontend/src/components/Quiz.vue:288
msgid "Resume Video"
msgstr ""

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr ""

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr ""

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr ""

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr ""

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr ""

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr ""

#: frontend/src/pages/ProfileRoles.vue:117
msgid "Role updated successfully"
msgstr ""

#: frontend/src/components/AppSidebar.vue:612
msgid "Roles"
msgstr ""

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr ""

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:56
#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr ""

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr ""

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr ""

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr ""

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:12
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/Code.vue:24
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/Question.vue:112
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/components/Settings/BadgeAssignmentForm.vue:12
#: frontend/src/components/Settings/BadgeForm.vue:78
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:101
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:9
#: frontend/src/pages/QuizForm.vue:43 frontend/src/pages/QuizSubmission.vue:14
#: frontend/src/pages/Quizzes.vue:105
msgid "Save"
msgstr ""

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr ""

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr ""

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr ""

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr ""

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:25
#: frontend/src/components/Settings/Members.vue:25
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:18
msgid "Search by Member Name"
msgstr ""

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr ""

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr ""

#: frontend/src/pages/Assignments.vue:34
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:47
msgid "Search by title"
msgstr ""

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr ""

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:154
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr ""

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr ""

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr ""

#: frontend/src/components/Settings/BadgeAssignmentForm.vue:42
msgid "Select Date"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:23
msgid "Select a Programming Exercise"
msgstr ""

#: frontend/src/components/Modals/Question.vue:101
msgid "Select a question"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select a quiz"
msgstr ""

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:35
msgid "Select an assignment"
msgstr ""

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr ""

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr ""

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr ""

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr ""

#: frontend/src/components/AppSidebar.vue:560
msgid "Setting up"
msgstr ""

#: frontend/src/components/AppSidebar.vue:605
msgid "Setting up payment gateway"
msgstr ""

#: frontend/src/components/AppSidebar.vue:610
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:53 frontend/src/pages/CourseForm.vue:152
#: frontend/src/pages/ProfileRoles.vue:4
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:19
#: frontend/src/pages/QuizForm.vue:86
msgid "Settings"
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr ""

#: frontend/src/pages/BatchForm.vue:42
msgid "Short Description"
msgstr ""

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:86
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr ""

#: frontend/src/pages/BatchForm.vue:45
msgid "Short description of the batch"
msgstr ""

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr ""

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:93 frontend/src/pages/Quizzes.vue:256
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr ""

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:98 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr ""

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr ""

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr ""

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr ""

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr ""

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:105 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr ""

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr ""

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr ""

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr ""

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr ""

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr ""

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr ""

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr ""

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr ""

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr ""

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr ""

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr ""

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr ""

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr ""

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr ""

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr ""

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#. Label of the source (Data) field in DocType 'LMS Video Watch Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Source"
msgstr ""

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr ""

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr ""

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:81
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr ""

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:82
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr ""

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:76
#: frontend/src/pages/Lesson.vue:45 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr ""

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr ""

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr ""

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr ""

#: frontend/src/components/Quiz.vue:81
msgid "Start the Quiz"
msgstr ""

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr ""

#: frontend/src/pages/Billing.vue:83
msgid "State/Province"
msgstr ""

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr ""

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#. Label of the status (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the status (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/Badges.vue:228
#: frontend/src/components/Settings/ZoomSettings.vue:197
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:280
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Status"
msgstr ""

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr ""

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr ""

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr ""

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr ""

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr ""

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr ""

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr ""

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr ""

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr ""

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:353
msgid "Submission saved!"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:254
msgid "Submissions deleted successfully"
msgstr ""

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Quiz.vue:242 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr ""

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr ""

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr ""

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr ""

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr ""

#: lms/lms/api.py:1119
msgid "Suspicious pattern found in {0}: {1}"
msgstr ""

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr ""

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr ""

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr ""

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr ""

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr ""

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr ""

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr ""

#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise'
#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:29
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:83
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Test Cases"
msgstr ""

#: frontend/src/pages/QuizForm.vue:23
msgid "Test Quiz"
msgstr ""

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:82
msgid "Test this Exercise"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:92
msgid "Test {0}"
msgstr ""

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr ""

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr ""

#: lms/lms/utils.py:1975
msgid "The batch is full. Please contact the Administrator."
msgstr ""

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr ""

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr ""

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr ""

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr ""

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr ""

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr ""

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr ""

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr ""

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr ""

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr ""

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr ""

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr ""

#: frontend/src/components/Settings/BadgeAssignments.vue:92
msgid "This badge has not been assigned to any students yet"
msgstr ""

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr ""

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:126
msgid "This course has:"
msgstr ""

#: lms/lms/utils.py:1818
msgid "This course is free."
msgstr ""

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr ""

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr ""

#: frontend/src/pages/Lesson.vue:30
msgid "This lesson is locked"
msgstr ""

#: frontend/src/pages/Lesson.vue:35
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr ""

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr ""

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr ""

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr ""

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr ""

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:58
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr ""

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr ""

#: frontend/src/components/VideoBlock.vue:140
msgid "Time for a Quiz"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr ""

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr ""

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr ""

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr ""

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:114
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr ""

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Programming Exercise'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/components/Settings/BadgeForm.vue:19
#: frontend/src/pages/Assignments.vue:162 frontend/src/pages/BatchForm.vue:27
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:17
#: frontend/src/pages/Programs.vue:101 frontend/src/pages/QuizForm.vue:56
#: frontend/src/pages/Quizzes.vue:115 frontend/src/pages/Quizzes.vue:229
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr ""

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr ""

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr ""

#: lms/lms/utils.py:1829
msgid "To join this batch, please contact the Administrator."
msgstr ""

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr ""

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr ""

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:73 frontend/src/pages/Quizzes.vue:235
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr ""

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr ""

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr ""

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr ""

#: frontend/src/components/Quiz.vue:284 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr ""

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr ""

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:40 frontend/src/pages/Assignments.vue:167
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:53
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr ""

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr ""

#: frontend/src/components/Quiz.vue:646
msgid "Type your answer"
msgstr ""

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr ""

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr ""

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr ""

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr ""

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr ""

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr ""

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr ""

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr ""

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:171
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr ""

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr ""

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr ""

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr ""

#: frontend/src/components/Controls/Uploader.vue:20
#: frontend/src/pages/BatchForm.vue:227 frontend/src/pages/CourseForm.vue:117
msgid "Upload"
msgstr ""

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr ""

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr ""

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: frontend/src/components/Settings/BadgeForm.vue:196
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr ""

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr ""

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr ""

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr ""

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr ""

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr ""

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr ""

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr ""

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr ""

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr ""

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr ""

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr ""

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr ""

#: frontend/src/pages/Lesson.vue:19
msgid "Video Statistics"
msgstr ""

#: frontend/src/components/Modals/VideoStatistics.vue:6
msgid "Video Statistics for {0}"
msgstr ""

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr ""

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr ""

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:73
msgid "Visit Batch"
msgstr ""

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr ""

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr ""

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr ""

#. Label of the watch_time (Data) field in DocType 'LMS Video Watch Duration'
#: frontend/src/components/Modals/VideoStatistics.vue:25
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Watch Time"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr ""

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr ""

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr ""

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr ""

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr ""

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr ""

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr ""

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr ""

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr ""

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr ""

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr ""

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr ""

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr ""

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr ""

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr ""

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr ""

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr ""

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr ""

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr ""

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr ""

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr ""

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr ""

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr ""

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr ""

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr ""

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr ""

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr ""

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr ""

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr ""

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr ""

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr ""

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr ""

#: frontend/src/components/Quiz.vue:265
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr ""

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr ""

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr ""

#: frontend/src/components/Quiz.vue:96 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr ""

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr ""

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr ""

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:181
msgid "You have been enrolled in this batch"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:229
msgid "You have been enrolled in this course"
msgstr ""

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr ""

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr ""

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:217
msgid "You need to login first to enroll for this course"
msgstr ""

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr ""

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr ""

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr ""

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr ""

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr ""

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:119
msgid "Your Output"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr ""

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr ""

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr ""

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr ""

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr ""

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr ""

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr ""

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr ""

#: frontend/src/components/Quiz.vue:258
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr ""

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr ""

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:171
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:164
msgid "Zoom Account created successfully"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:202
msgid "Zoom Account updated successfully"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr ""

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr ""

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr ""

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr ""

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr ""

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr ""

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr ""

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr ""

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr ""

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr ""

#: frontend/src/pages/Lesson.vue:98 frontend/src/pages/Lesson.vue:234
msgid "completed"
msgstr ""

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr ""

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr ""

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr ""

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr ""

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr ""

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr ""

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr ""

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr ""

#: frontend/src/pages/QuizForm.vue:344
msgid "question_detail"
msgstr ""

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr ""

#: lms/templates/reviews.html:43
msgid "stars"
msgstr ""

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr ""

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr ""

#: frontend/src/pages/Assignments.vue:26
msgid "{0} Assignments"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:39
msgid "{0} Exercises"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:14
msgid "{0} Members"
msgstr ""

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr ""

#: frontend/src/pages/Quizzes.vue:18
msgid "{0} Quizzes"
msgstr ""

#: lms/lms/api.py:886 lms/lms/api.py:894
msgid "{0} Settings not found"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:12
msgid "{0} Submissions"
msgstr ""

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr ""

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr ""

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr ""

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr ""

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr ""

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr ""

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr ""

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr ""

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr ""

#: lms/lms/utils.py:686
msgid "{0} mentioned you in a comment"
msgstr ""

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr ""

#: lms/lms/utils.py:639 lms/lms/utils.py:645
msgid "{0} mentioned you in a comment in {1}"
msgstr ""

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr ""

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr ""

