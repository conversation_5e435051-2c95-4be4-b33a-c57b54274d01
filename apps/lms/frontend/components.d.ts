/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Annoucements: typeof import('./src/components/Annoucements.vue')['default']
    AnnouncementModal: typeof import('./src/components/Modals/AnnouncementModal.vue')['default']
    Apps: typeof import('./src/components/Apps.vue')['default']
    AppSidebar: typeof import('./src/components/AppSidebar.vue')['default']
    AssessmentModal: typeof import('./src/components/Modals/AssessmentModal.vue')['default']
    AssessmentPlugin: typeof import('./src/components/AssessmentPlugin.vue')['default']
    Assessments: typeof import('./src/components/Assessments.vue')['default']
    Assignment: typeof import('./src/components/Assignment.vue')['default']
    AssignmentForm: typeof import('./src/components/Modals/AssignmentForm.vue')['default']
    AudioBlock: typeof import('./src/components/AudioBlock.vue')['default']
    Autocomplete: typeof import('./src/components/Controls/Autocomplete.vue')['default']
    BadgeAssignmentForm: typeof import('./src/components/Settings/BadgeAssignmentForm.vue')['default']
    BadgeAssignments: typeof import('./src/components/Settings/BadgeAssignments.vue')['default']
    BadgeForm: typeof import('./src/components/Settings/BadgeForm.vue')['default']
    Badges: typeof import('./src/components/Settings/Badges.vue')['default']
    BatchCard: typeof import('./src/components/BatchCard.vue')['default']
    BatchCourseModal: typeof import('./src/components/Modals/BatchCourseModal.vue')['default']
    BatchCourses: typeof import('./src/components/BatchCourses.vue')['default']
    BatchDashboard: typeof import('./src/components/BatchDashboard.vue')['default']
    BatchFeedback: typeof import('./src/components/BatchFeedback.vue')['default']
    BatchOverlay: typeof import('./src/components/BatchOverlay.vue')['default']
    BatchStudentProgress: typeof import('./src/components/Modals/BatchStudentProgress.vue')['default']
    BatchStudents: typeof import('./src/components/BatchStudents.vue')['default']
    BrandSettings: typeof import('./src/components/Settings/BrandSettings.vue')['default']
    BulkCertificates: typeof import('./src/components/Modals/BulkCertificates.vue')['default']
    Categories: typeof import('./src/components/Settings/Categories.vue')['default']
    CertificationLinks: typeof import('./src/components/CertificationLinks.vue')['default']
    ChapterModal: typeof import('./src/components/Modals/ChapterModal.vue')['default']
    ChildTable: typeof import('./src/components/Controls/ChildTable.vue')['default']
    Code: typeof import('./src/components/Controls/Code.vue')['default']
    CodeEditor: typeof import('./src/components/Controls/CodeEditor.vue')['default']
    CollapseSidebar: typeof import('./src/components/Icons/CollapseSidebar.vue')['default']
    CourseCard: typeof import('./src/components/CourseCard.vue')['default']
    CourseCardOverlay: typeof import('./src/components/CourseCardOverlay.vue')['default']
    CourseInstructors: typeof import('./src/components/CourseInstructors.vue')['default']
    CourseOutline: typeof import('./src/components/CourseOutline.vue')['default']
    CourseProgressSummary: typeof import('./src/components/Modals/CourseProgressSummary.vue')['default']
    CourseReviews: typeof import('./src/components/CourseReviews.vue')['default']
    CreateOutline: typeof import('./src/components/CreateOutline.vue')['default']
    DateRange: typeof import('./src/components/Common/DateRange.vue')['default']
    DesktopLayout: typeof import('./src/components/DesktopLayout.vue')['default']
    DiscussionModal: typeof import('./src/components/Modals/DiscussionModal.vue')['default']
    DiscussionReplies: typeof import('./src/components/DiscussionReplies.vue')['default']
    Discussions: typeof import('./src/components/Discussions.vue')['default']
    EditCoverImage: typeof import('./src/components/Modals/EditCoverImage.vue')['default']
    EditProfile: typeof import('./src/components/Modals/EditProfile.vue')['default']
    EmailTemplateModal: typeof import('./src/components/Modals/EmailTemplateModal.vue')['default']
    EmailTemplates: typeof import('./src/components/Settings/EmailTemplates.vue')['default']
    EmptyState: typeof import('./src/components/EmptyState.vue')['default']
    EvaluationModal: typeof import('./src/components/Modals/EvaluationModal.vue')['default']
    Evaluators: typeof import('./src/components/Settings/Evaluators.vue')['default']
    Event: typeof import('./src/components/Modals/Event.vue')['default']
    ExplanationVideos: typeof import('./src/components/Modals/ExplanationVideos.vue')['default']
    FeedbackModal: typeof import('./src/components/Modals/FeedbackModal.vue')['default']
    FrappeCloudIcon: typeof import('./src/components/Icons/FrappeCloudIcon.vue')['default']
    IconPicker: typeof import('./src/components/Controls/IconPicker.vue')['default']
    IndicatorIcon: typeof import('./src/components/Icons/IndicatorIcon.vue')['default']
    InviteIcon: typeof import('./src/components/Icons/InviteIcon.vue')['default']
    JobApplicationModal: typeof import('./src/components/Modals/JobApplicationModal.vue')['default']
    JobCard: typeof import('./src/components/JobCard.vue')['default']
    LessonContent: typeof import('./src/components/LessonContent.vue')['default']
    LessonHelp: typeof import('./src/components/LessonHelp.vue')['default']
    Link: typeof import('./src/components/Controls/Link.vue')['default']
    LiveClass: typeof import('./src/components/LiveClass.vue')['default']
    LiveClassAttendance: typeof import('./src/components/Modals/LiveClassAttendance.vue')['default']
    LiveClassModal: typeof import('./src/components/Modals/LiveClassModal.vue')['default']
    LMSLogo: typeof import('./src/components/Icons/LMSLogo.vue')['default']
    Members: typeof import('./src/components/Settings/Members.vue')['default']
    MobileLayout: typeof import('./src/components/MobileLayout.vue')['default']
    MultiSelect: typeof import('./src/components/Controls/MultiSelect.vue')['default']
    NoPermission: typeof import('./src/components/NoPermission.vue')['default']
    NoSidebarLayout: typeof import('./src/components/NoSidebarLayout.vue')['default']
    NotPermitted: typeof import('./src/components/NotPermitted.vue')['default']
    PageModal: typeof import('./src/components/Modals/PageModal.vue')['default']
    PaymentSettings: typeof import('./src/components/Settings/PaymentSettings.vue')['default']
    Play: typeof import('./src/components/Icons/Play.vue')['default']
    ProgressBar: typeof import('./src/components/ProgressBar.vue')['default']
    Question: typeof import('./src/components/Modals/Question.vue')['default']
    Quiz: typeof import('./src/components/Quiz.vue')['default']
    QuizBlock: typeof import('./src/components/QuizBlock.vue')['default']
    QuizInVideo: typeof import('./src/components/Modals/QuizInVideo.vue')['default']
    Rating: typeof import('./src/components/Controls/Rating.vue')['default']
    RelatedCourses: typeof import('./src/components/RelatedCourses.vue')['default']
    ReviewModal: typeof import('./src/components/Modals/ReviewModal.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SettingDetails: typeof import('./src/components/Settings/SettingDetails.vue')['default']
    SettingFields: typeof import('./src/components/Settings/SettingFields.vue')['default']
    Settings: typeof import('./src/components/Settings/Settings.vue')['default']
    SidebarLink: typeof import('./src/components/SidebarLink.vue')['default']
    StudentHeatmap: typeof import('./src/components/StudentHeatmap.vue')['default']
    StudentModal: typeof import('./src/components/Modals/StudentModal.vue')['default']
    Tags: typeof import('./src/components/Tags.vue')['default']
    UnsplashImageBrowser: typeof import('./src/components/UnsplashImageBrowser.vue')['default']
    UpcomingEvaluations: typeof import('./src/components/UpcomingEvaluations.vue')['default']
    Uploader: typeof import('./src/components/Controls/Uploader.vue')['default']
    UploadPlugin: typeof import('./src/components/UploadPlugin.vue')['default']
    UserAvatar: typeof import('./src/components/UserAvatar.vue')['default']
    UserDropdown: typeof import('./src/components/UserDropdown.vue')['default']
    VideoBlock: typeof import('./src/components/VideoBlock.vue')['default']
    VideoStatistics: typeof import('./src/components/Modals/VideoStatistics.vue')['default']
    ZoomAccountModal: typeof import('./src/components/Modals/ZoomAccountModal.vue')['default']
    ZoomSettings: typeof import('./src/components/Settings/ZoomSettings.vue')['default']
  }
}
