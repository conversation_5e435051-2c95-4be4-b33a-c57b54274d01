// Client Script for Salary Slip Approval Report - SIMPLIFIED VERSION
// This version focuses on core functionality with better error handling
// Paste this code into the Client Script section of your Script Report in Frappe Desk

frappe.query_reports["Report Test"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        },
        {
            "fieldname": "department",
            "label": __("Department"),
            "fieldtype": "Link",
            "options": "Department"
        },
        {
            "fieldname": "employee",
            "label": __("Employee"),
            "fieldtype": "Link",
            "options": "Employee"
        }
    ],

    "onload": function(report) {
        // Add submit all draft button
        report.page.add_inner_button(__("Submit All Draft"), function() {
            submit_all_draft_slips();
        });

        // Add select all checkbox functionality
        setTimeout(function() {
            add_select_all_functionality(report);
        }, 1000);
    }
};

// Add select all functionality to the report
function add_select_all_functionality(report) {
    // Add select all checkbox to the report header
    if (!$('.select-all-salary-slips').length) {
        $('.dt-scrollable').before(`
            <div style="padding: 10px; border-bottom: 1px solid #d1d8dd;">
                <label>
                    <input type="checkbox" class="select-all-salary-slips">
                    Select All Salary Slips
                </label>
                <button class="btn btn-primary btn-sm" style="margin-left: 15px;" onclick="submit_selected_slips()">
                    Submit Selected
                </button>
            </div>
        `);

        // Handle select all functionality
        $('.select-all-salary-slips').on('change', function() {
            let isChecked = $(this).is(':checked');
            $('.salary-slip-checkbox').prop('checked', isChecked);
        });
    }
}

function submit_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to submit all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: {
                        docstatus: 0
                    },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        submit_multiple_slips_batch(slip_names);
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

// Function to submit selected slips (called from button)
function submit_selected_slips() {
    let selected_names = [];
    $('.salary-slip-checkbox:checked').each(function() {
        selected_names.push($(this).data('name'));
    });

    if (selected_names.length === 0) {
        frappe.msgprint(__("Please select salary slips to submit"));
        return;
    }

    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [selected_names.length]),
        function() {
            submit_multiple_slips_batch(selected_names);
        }
    );
}

// Batch submission method - more reliable
function submit_multiple_slips_batch(slip_names) {
    let submitted_count = 0;
    let failed_count = 0;
    let total_count = slip_names.length;

    frappe.show_progress(__("Submitting Salary Slips"), 0, total_count, __("Please wait..."));

    slip_names.forEach(function(slip_name, index) {
        // Add delay to prevent overwhelming the server
        setTimeout(function() {
            frappe.call({
                method: "frappe.client.submit",
                args: {
                    doc: {
                        doctype: "Salary Slip",
                        name: slip_name
                    }
                },
                callback: function(r) {
                    if (r.exc) {
                        failed_count++;
                        console.log("Failed to submit:", slip_name, r.exc);
                    } else {
                        submitted_count++;
                    }

                    frappe.show_progress(__("Submitting Salary Slips"), submitted_count + failed_count, total_count);

                    if (submitted_count + failed_count === total_count) {
                        frappe.hide_progress();

                        let message = __("{0} salary slip(s) submitted successfully", [submitted_count]);
                        if (failed_count > 0) {
                            message += __("<br>{0} salary slip(s) failed to submit", [failed_count]);
                        }

                        frappe.msgprint({
                            message: message,
                            indicator: failed_count > 0 ? 'orange' : 'green'
                        });

                        // Refresh the report
                        if (window.cur_report_wrapper && window.cur_report_wrapper.report) {
                            window.cur_report_wrapper.report.refresh();
                        }
                    }
                },
                error: function(r) {
                    failed_count++;
                    console.log("Error submitting:", slip_name, r);

                    frappe.show_progress(__("Submitting Salary Slips"), submitted_count + failed_count, total_count);

                    if (submitted_count + failed_count === total_count) {
                        frappe.hide_progress();

                        let message = __("{0} salary slip(s) submitted successfully", [submitted_count]);
                        if (failed_count > 0) {
                            message += __("<br>{0} salary slip(s) failed to submit", [failed_count]);
                        }

                        frappe.msgprint({
                            message: message,
                            indicator: failed_count > 0 ? 'orange' : 'green'
                        });

                        // Refresh the report
                        if (window.cur_report_wrapper && window.cur_report_wrapper.report) {
                            window.cur_report_wrapper.report.refresh();
                        }
                    }
                }
            });
        }, index * 200); // 200ms delay between each submission
    });
}

// Individual submission method - fallback
function submit_multiple_slips_individual(slip_names) {
    let submitted_count = 0;
    let failed_count = 0;
    let total_count = slip_names.length;

    frappe.show_progress(__("Submitting Salary Slips"), 0, total_count, __("Please wait..."));

    slip_names.forEach(function(slip_name, index) {
        // Add delay to prevent overwhelming the server
        setTimeout(function() {
            frappe.call({
                method: "frappe.desk.form.save.savedocs",
                args: {
                    doc: JSON.stringify({
                        doctype: "Salary Slip",
                        name: slip_name
                    }),
                    action: "Submit"
                },
                callback: function(r) {
                    if (r.exc) {
                        failed_count++;
                        console.log("Failed to submit:", slip_name, r.exc);
                    } else {
                        submitted_count++;
                    }

                    frappe.show_progress(__("Submitting Salary Slips"), submitted_count + failed_count, total_count);

                    if (submitted_count + failed_count === total_count) {
                        frappe.hide_progress();

                        let message = __("{0} salary slip(s) submitted successfully", [submitted_count]);
                        if (failed_count > 0) {
                            message += __("<br>{0} salary slip(s) failed to submit", [failed_count]);
                        }

                        frappe.msgprint({
                            message: message,
                            indicator: failed_count > 0 ? 'orange' : 'green'
                        });

                        // Refresh the report
                        if (window.cur_report_wrapper && window.cur_report_wrapper.report) {
                            window.cur_report_wrapper.report.refresh();
                        }
                    }
                }
            });
        }, index * 100); // 100ms delay between each submission
    });
}
