// Client Script for Salary Slip Approval Report
// Paste this code into the Client Script section of your Script Report in Frappe Desk

frappe.query_reports["Salary Slip Approval Report"] = {
    "filters": [
        {
            "fieldname": "company",
            "label": __("Company"),
            "fieldtype": "Link",
            "options": "Company",
            "default": frappe.defaults.get_user_default("Company")
        },
        {
            "fieldname": "from_date",
            "label": __("From Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.add_months(frappe.datetime.get_today(), -1)
        },
        {
            "fieldname": "to_date",
            "label": __("To Date"),
            "fieldtype": "Date",
            "default": frappe.datetime.get_today()
        },
        {
            "fieldname": "department",
            "label": __("Department"),
            "fieldtype": "Link",
            "options": "Department"
        },
        {
            "fieldname": "employee",
            "label": __("Employee"),
            "fieldtype": "Link",
            "options": "Employee"
        }
    ],
    
    "onload": function(report) {
        // Add submit button for salary slips
        report.page.add_inner_button(__("Submit Selected"), function() {
            let selected_docs = report.get_checked_items();
            if (selected_docs.length === 0) {
                frappe.msgprint(__("Please select salary slips to submit"));
                return;
            }
            submit_salary_slips(selected_docs);
        });

        report.page.add_inner_button(__("Submit All Draft"), function() {
            submit_all_draft_slips();
        });
    }
};

function submit_all_draft_slips() {
    frappe.confirm(
        __("Are you sure you want to submit all draft salary slips?"),
        function() {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Salary Slip",
                    filters: {
                        docstatus: 0
                    },
                    fields: ["name"]
                },
                callback: function(r) {
                    if (r.message && r.message.length > 0) {
                        let slip_names = r.message.map(d => d.name);
                        submit_multiple_slips(slip_names);
                    } else {
                        frappe.msgprint(__("No draft salary slips found"));
                    }
                }
            });
        }
    );
}

function submit_salary_slips(selected_docs) {
    let slip_names = selected_docs.map(d => d.name);

    frappe.confirm(
        __("Are you sure you want to submit {0} salary slip(s)?", [slip_names.length]),
        function() {
            submit_multiple_slips(slip_names);
        }
    );
}

function submit_multiple_slips(slip_names) {
    let submitted_count = 0;
    let failed_count = 0;
    let total_count = slip_names.length;

    frappe.show_progress(__("Submitting Salary Slips"), 0, total_count, __("Please wait..."));

    slip_names.forEach(function(slip_name) {
        frappe.call({
            method: "frappe.client.submit_doc",
            args: {
                doc: {
                    doctype: "Salary Slip",
                    name: slip_name
                }
            },
            callback: function(r) {
                if (r.exc) {
                    failed_count++;
                } else {
                    submitted_count++;
                }

                frappe.show_progress(__("Submitting Salary Slips"), submitted_count + failed_count, total_count);

                if (submitted_count + failed_count === total_count) {
                    frappe.hide_progress();

                    let message = __("{0} salary slip(s) submitted successfully", [submitted_count]);
                    if (failed_count > 0) {
                        message += __("<br>{0} salary slip(s) failed to submit", [failed_count]);
                    }

                    frappe.msgprint({
                        message: message,
                        indicator: failed_count > 0 ? 'orange' : 'green'
                    });

                    frappe.query_report.refresh();
                }
            }
        });
    });
}
